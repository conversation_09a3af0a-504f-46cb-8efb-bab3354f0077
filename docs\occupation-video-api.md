# 职业视频 API 接口文档

## 接口概述

职业视频模块提供了职业视频查询和职业类型管理的相关接口。

## 接口列表

### 1. 职业视频列表

**接口地址：** `GET /api/school/xuezhi/occupation-video/list`

**请求参数：**
- `occupation_type_id` (可选): 职业类型ID，整数，必须存在于 occupation_type 表中
- `xzq_id` (可选): 地区ID，整数，必须大于0
- `title` (可选): 视频标题，支持模糊搜索，最大255个字符
- `per_page` (可选): 每页数量，整数，范围1-100，默认15
- `page` (可选): 页码，整数，范围1-10000，默认1

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "data": [
            {
                "id": 1,
                "title": "软件工程师职业介绍",
                "vid": "video123",
                "xzq_id": 110000,
                "cover": "https://example.com/cover.jpg",
                "occupation_type_id": 1,
                "video_id": 123,
                "occupation_type": {
                    "id": 1,
                    "occupation_name": "计算机类"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 10,
            "per_page": 15,
            "total": 150,
            "from": 1,
            "to": 15,
            "has_more_pages": true,
            "next_page_url": "http://example.com/api/school/xuezhi/occupation-video/list?page=2",
            "prev_page_url": null,
            "path": "http://example.com/api/school/xuezhi/occupation-video/list"
        }
    }
}
```

### 2. 职业视频详情

**接口地址：** `GET /api/school/xuezhi/occupation-video/{id}`

**路径参数：**
- `id`: 职业视频ID

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "title": "软件工程师职业介绍",
        "vid": "video123",
        "xzq_id": 110000,
        "cover": "https://example.com/cover.jpg",
        "occupation_type_id": 1,
        "video_id": 123,
        "occupation_type": {
            "id": 1,
            "occupation_name": "计算机类"
        }
    }
}
```

### 3. 职业类型列表

**接口地址：** `GET /api/school/xuezhi/occupation-type/list`

**请求参数：**
- `occupation_name` (可选): 职业名称，支持模糊搜索
- `has_videos` (可选): 是否只返回有视频的职业类型，布尔值

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "occupation_name": "计算机类"
        },
        {
            "id": 2,
            "occupation_name": "医学类"
        }
    ]
}
```

### 4. 职业类型详情

**接口地址：** `GET /api/school/xuezhi/occupation-type/{id}`

**路径参数：**
- `id`: 职业类型ID

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "occupation_name": "计算机类"
    }
}
```

### 5. 职业类型及视频数量统计

**接口地址：** `GET /api/school/xuezhi/occupation-type/with-count`

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "occupation_name": "计算机类",
            "video_count": 25
        },
        {
            "id": 2,
            "occupation_name": "医学类",
            "video_count": 18
        }
    ]
}
```

## 错误响应

当请求出现错误时，接口会返回相应的错误信息：

```json
{
    "code": 404,
    "message": "职业视频不存在",
    "data": null
}
```

## 状态码说明

- `200`: 请求成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `422`: 验证失败
- `500`: 服务器内部错误
