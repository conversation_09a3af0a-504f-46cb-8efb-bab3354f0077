<?php

namespace App\Services;

use App\Constants\RedisKeyConstants;
use App\Exceptions\BusinessException;
use App\Models\Admin\Organization;
use App\Models\Admin\OrganizationHasMenu;
use App\Models\Role;
use App\Models\RoleHasMenu;
use App\Services\Admin\MenuService;
use App\Services\Tool\RedisUtilService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;

class RoleService extends BaseService
{
    protected $menuService;
    protected $redisUtils;

    // 构造函数
    public function __construct(MenuService $menuService, RedisUtilService $redisUtils)
    {
        $this->menuService = $menuService;
        $this->redisUtils = $redisUtils;
    }

    public function listBuilder(Request $request)
    {
        $organization_id = $this->getOrganizationId($request);
        return Role::query()->select(['id', 'name', 'organization_id', 'type', 'status', 'creator', 'updater'])
            ->where('organization_id', $organization_id)
            ->orderBy('id', 'desc')
            ->get();
    }

    //创建角色
    public function createRole(array $data, $organizationId, $realName)
    {
        $data['organization_id'] = $organizationId;
        $data['creator'] = $realName;
        $data['updater'] = $realName;

        // 获取guard_name
        $guardName = request()->route()->getAction('guard') ?? config('auth.defaults.guard');
        $data['guard_name'] = $guardName;

        return Role::forceCreate($data);
    }

    // 获取机构购买拥有的菜单列表
    public function getOrgHasMenus2(Request $request)
    {
        $organization_id = $this->getOrganizationId($request);
        $key = keyBuilder(RedisKeyConstants::SYSTEM['ORG_MENUS'], $organization_id);
        if (Redis::exists($key)) {
            $cacheValue = Redis::get($key);
            return is_string($cacheValue) ? json_decode($cacheValue, true) : $cacheValue;
        }

        $result = OrganizationHasMenu::join('menus', 'menus.id', '=', 'organization_has_menus.menu_id')
            ->select('organization_has_menus.id', 'organization_has_menus.menu_id', 'organization_has_menus.parent_id',
                'organization_has_menus.date_start','organization_has_menus.date_due', 'menus.crowd')
            ->selectRaw("COALESCE(NULLIF(organization_has_menus.menu_alias, ''), menus.menu_name) as menu_name")
            ->where('menus.status', 1) // 基础菜单表状态正常的菜单
            ->where('organization_has_menus.status', 1) // 已购拥有菜单表状态正常的菜单
            ->where('organization_has_menus.organization_id', $organization_id)
            ->orderBy('organization_has_menus.sort', 'asc')
            ->get()
            ->map(function ($item) {
                // 处理 crowd 字段，确保它是 JSON 格式
                $item->crowd = empty($item->crowd) ? [] : json_decode($item->crowd, true);
                return $item;
            });
        $menu_list = [];
        if ($result->isNotEmpty()) {
            // 获取菜单树形结构
            $menu_list = $this->menuService->getTreeMenus($result, 1);
        }

        if (!empty($menu_list)) {
            Redis::setex($key, 60 * 60, json_encode($menu_list));
        }

        return $menu_list;
    }

    // 获取机构购买拥有的菜单列表
    public function getOrgRoleTypes(Request $request)
    {
        $organization_id = $this->getOrganizationId($request);
        $organization = Organization::find($organization_id);

        // 角色类型 1学生 2教务 3老师 4教育局 5家长 999管理员
        $roleTypes = [];
        // 管理员
        if($organization_id == 1){
            $roleTypes = [['type'=>999, 'name'=>'管理员']];
        } else if ($organization->model_type == 'school'){
            $roleTypes = [
                ['type'=>1, 'name'=>'学生'],
                ['type'=>2, 'name'=>'教务'],
                ['type'=>3, 'name'=>'老师']
            ];
        } else if ($organization->model_type == 'partner'){
            $roleTypes = [
                ['type'=>4, 'name'=>'教育局']
            ];
        }

        return $roleTypes;
    }

    // 设置角色拥有的菜单
    public function setRoleHasMenus(Request $request, $role_id)
    {
        try {
            $menu_ids = $request->menu_ids;
            // 删除角色拥有的菜单
            RoleHasMenu::where('role_id', $role_id)->delete();

            // 添加角色拥有的菜单
            foreach ($menu_ids as $menu_id) {
                RoleHasMenu::forceCreate([
                    'role_id' => $role_id,
                    'organization_menu_id' => $menu_id,
                    'creator' => $request->user()->real_name
                ]);
            }

            // 删除所有角色菜单缓存
            $this->redisUtils->deleteCacheByPrefix(RedisKeyConstants::SYSTEM['ROLE_MENUS']);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getOrganizationId(Request $request)
    {
        // 获取organization_id：如果请求参数中没有organization_id，则取当前登录用户的organization_id
        $organization_id = $request->input('organization_id', $request->user()->organization_id);

        // 校验不能为空
        if ($organization_id === null || $organization_id === '') {
            throw new BusinessException("机构ID不能为空逻辑错误");
        }

        // 校验为正整数并返回整型
        if (filter_var($organization_id, FILTER_VALIDATE_INT, ['options' => ['min_range' => 1]]) === false) {
            throw new BusinessException("机构ID必须为正整数");
        }

        return (int) $organization_id;
    }

    
    // 禁用启用角色
    public function updateRoleStatus(Request $request, $role_id)
    {
        $status = $request->input('status');
        // 校验参数
        $validator = Validator::make($request->all(), [
            'status' => 'in:1,2',
        ], [
            'status.in' => '状态值必须为1或2',
        ]);
        if ($validator->fails()) {
            throw new BusinessException($validator->errors()->first(), 400);
        }
        $role = Role::find($role_id);
        if (!$role) {
            throw new BusinessException("角色不存在");
        }
        $role->status = $status;
        $role->updater = $request->user()->real_name;
        return $role->save();
    }

}
