<?php

use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\OrganizationController;
use App\Http\Controllers\Admin\StudentFindpassController;
use App\Http\Controllers\ModelChangeLogController;
use App\Http\Controllers\Partner\PartnerController;
use App\Http\Controllers\School\System\SchoolAssessmentController;
use App\Http\Controllers\School\System\SchoolController;
use App\Http\Controllers\Admin\AssessmentTemplateController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 后台管理端相关模块路由
Route::group(['prefix' => 'admin', 'middleware' => ['auth.refresh']], function () {
    // 菜单列表
    Route::get('menus/list', [MenuController::class, 'index'])->name('admin.menus.list');
    // 菜单详情
    Route::get('menus/{id}', [MenuController::class, 'show'])->name('admin.menus.show')->where('id', '[0-9]+');
    Route::post('menus', [MenuController::class, 'store'])->name('admin.menus.store');
    Route::put('menus/{id}', [MenuController::class, 'update'])->name('admin.menus.update')->where('id', '[0-9]+');
    Route::put('menus/changeStatus/{id}', [MenuController::class, 'updateStatus'])->name('admin.menus.changeStatus')->where('id', '[0-9]+');
    Route::get('menus/get_children_menus_by_parent_id', [MenuController::class, 'getChildrenMenusByParentId'])->name('admin.menus.getChildrenMenusByParentId');
    Route::post('menus/set_menu_sort', [MenuController::class, 'setMenuSort'])->name('admin.menus.setMenuSort');
    // 测评模板列表
    Route::get('assessment_template/list', [AssessmentTemplateController::class, 'index'])->name('admin.assessment_template.list');
    //测评模板详情
    Route::get('assessment_template/getTemplateTypes', [AssessmentTemplateController::class, 'getTemplateTypes'])->name('admin.assessment_template.getTemplateTypes');
    Route::get('assessment_template/show', [AssessmentTemplateController::class, 'show'])->name('admin.assessment_template.show');
    Route::get('assessment_template/Single', [AssessmentTemplateController::class, 'Single'])->name('admin.assessment_template.Single');
    Route::put('assessment_template/{id}', [AssessmentTemplateController::class, 'update'])->name('admin.assessment_template.update');
    Route::post('assessment_template/store', [AssessmentTemplateController::class, 'store'])->name('admin.assessment_template.store');
    // 后台管理端查看学校列表
    Route::get('schools', [OrganizationController::class, 'getSchools'])->name('admin.schools.list');
    // 后台管理端新增学校
    Route::post('schools', [OrganizationController::class, 'createSchool'])->name('admin.schools.create');
    // 后台管理端查看学校详情
    Route::get('schools/{id}', [SchoolController::class, 'show'])->name('admin.schools.show')->where('id', '[0-9]+');
    // 后台管理端更新学校
    Route::put('schools/{id}', [OrganizationController::class, 'updateSchool'])->name('admin.schools.update')->where('id', '[0-9]+');
    //学校测评列表
    Route::get('school_assessments/{school_id}', [SchoolAssessmentController::class, 'index'])->name('admin.school_assessments.list')->where('school_id', '[0-9]+');
    //设置学校测评
    Route::post('school_assessments/{school_id}', [SchoolAssessmentController::class, 'setSchoolAssessments'])->name('admin.school_assessments.setSchoolAssessments');
    // 设置学校某个测评是否开启普测
    Route::put('school_assessments/{school_id}/open_puce', [SchoolAssessmentController::class, 'setSchoolAssessmentOpenPuce'])->name('admin.school_assessments.openPuce');
    Route::get('assessments', [SchoolAssessmentController::class, 'assessments'])->name('admin.school_assessments.assessments');

    // 后台管理端查看教育局列表
    Route::get('partners', [OrganizationController::class, 'getPartners'])->name('admin.partners.list');
    // 后台管理端新增教育局
    Route::post('partners', [OrganizationController::class, 'createPartner'])->name('admin.partners.create');
    // 后台管理端查看教育局详情
    Route::get('partners/{id}', [PartnerController::class, 'show'])->name('admin.partners.show')->where('id', '[0-9]+');
    Route::get('partner/{id}', [PartnerController::class, 'showDetail'])->name('admin.partners.showDetail')->where('id', '[0-9]+');
    // 后台管理端更新教育局
    Route::put('partners/{id}', [OrganizationController::class, 'updatePartner'])->name('admin.partners.update')->where('id', '[0-9]+');


//    // 后台管理端查看教育局、学校角色列表
//    Route::get('organization/{organization_id}/role_list', [OrganizationController::class, 'roleList'])->name('admin.organization.role_list')->where('organization_id', '[0-9]+');
//    // 后台管理端查看教育局、学校用户列表
    // 后台管理端查看机构下教务用户列表
    Route::get('organization/{organization_id}/edu_admins', [OrganizationController::class, 'eduAdminList'])->name('admin.organization.edu_admins')->where('organization_id', '[0-9]+');

//    Route::get('organization/{organization_id}/user_list', [OrganizationController::class, 'userList'])->name('admin.organization.user_list')->where('organization_id', '[0-9]+');
    Route::get('organization/{school_id}/school_campuses', [OrganizationController::class, 'schoolCampuses'])->name('admin.organization.school_campuses')->where('school_id', '[0-9]+');
    Route::get('organization/{partner_id}/partner_campuses', [OrganizationController::class, 'partnerCampuses'])->name('admin.organization.partner_campuses')->where('partner_id', '[0-9]+');
    // 后台管理端获取机构初始已选的菜单（购买模块）
    Route::get('organization/{organization_id}/org_init_menus', [OrganizationController::class, 'getOrganizationHasMenus'])->name('admin.organization.org_init_menus')->where('organization_id', '[0-9]+');
    // 后台管理端设置机构拥有的初始菜单（购买模块）
    Route::post('organization/{organization_id}/set_org_menus', [OrganizationController::class, 'setOrganizationHasMenus'])->name('admin.organization.set_org_menus')->where('organization_id', '[0-9]+');
    // 设置当前机构下的学校列表
    Route::post('organization/{partner_id}/set_partner_campuses', [OrganizationController::class, 'setPartnerCampuses'])->name('admin.organization.set_partner_campuses');

    //更新菜单
    Route::put('organization/update_menu_alias', [OrganizationController::class, 'updateMenuAlias'])->name('admin.organization.update_menu_alias');

    // 学生找回密码管理
    Route::prefix('student-findpass')->group(function () {
        // 获取申请列表
        Route::get('/', [StudentFindpassController::class, 'index'])->name('admin.student-findpass.index');
        // 处理申请
        Route::post('process/{id}', [StudentFindpassController::class, 'process'])->name('admin.student-findpass.process')->where('id', '[0-9]+');
    });

    // 模型变更日志
    Route::get('/model_change_logs', [ModelChangeLogController::class, 'index'])->name('admin.modelChangeLog.index');
});
