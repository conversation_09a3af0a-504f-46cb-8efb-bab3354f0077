<?php

namespace App\Traits;

trait DingDingMessage
{
    /**
     * @param $content string   需要发送的信息
     * @return bool  true|false
     */
    function send_dingding_message($content, $title = '业务报警', $workId = 1)
    {
        $url = $this->getUrl($workId);
        $msg1 = [
            'msgtype' => 'markdown',//这是文件发送类型，可以根据需求调整
            'markdown' => [
                'title' => $title,
                'text' => $content,
            ],
        ];
        $data_string = json_encode($msg1);
        return $this->request_by_curl($url, $data_string);
    }


    function send_dingding_str_message($str, $workId = 1)
    {
        $url = $this->getUrl($workId);
        $msg1 = [
            'msgtype' => 'text',//这是文件发送类型，可以根据需求调整
            'text' => [
                'content' => $str,
            ],
        ];
        $data_string = json_encode($msg1);
        return $this->request_by_curl($url, $data_string);
    }


    function request_by_curl($remote_server, $post_string)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $remote_server);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    public function getUrl($workId): string
    {
        // $url = env('DingTalk_WebHook');
        // $secret = env('DingTalk_Secret');
        if ($workId == 1) {
            // 异常群
            $url = "https://oapi.dingtalk.com/robot/send?access_token=4f560e91d6746bad66e9c20bbd5b4150c41ddf7ea3884db7f58a84311cc342d7";
            $secret = "SECa6ddbad3266500209e9c140329bd8042a30836c03a9bba6868a6b6ced48c705c";
        } else {
            // 报警群
            $url = "https://oapi.dingtalk.com/robot/send?access_token=d1984dc8351b0d9e7479d7178a677fb4ee0d12900aa60f2862900026df22c0d2";
            $secret = "SECef8a5d7f78ded3bc99088e2f0fbee4565b3ba31058a179b517c4530d4bc6fc10";
        }
        // 第一步，把timestamp+"\n"+密钥当做签名字符串，使用HmacSHA256算法计算签名，然后进行Base64 encode，最后再把签名参数再进行urlEncode，得到最终的签名（需要使用UTF-8字符集）。
        $time = time() * 1000;//毫秒级时间戳，我这里为了方便，直接把时间*1000了
        $sign = hash_hmac('sha256', $time . "\n" . $secret, $secret, true);
        $sign = base64_encode($sign);
        $sign = urlencode($sign);
        $url = "{$url}&timestamp={$time}&sign={$sign}";
        return $url;
    }
}
