<?php

namespace App\Services\School\Xuezhi;

use App\Constants\BaseConstants;
use App\Constants\MajorConstants;
use App\Enums\PhaseEnum;
use App\Enums\ProvinceEnum;
use App\Exceptions\BusinessException;
use App\Http\Requests\School\Xuezhi\MajorRequest;
use App\Models\Gk\College;
use App\Models\Gk\MajorAcademicGroupBK;
use App\Models\Gk\MajorAcademicGroupCourse;
use App\Models\Gk\MajorAverageMonthlySalary;
use App\Models\Gk\MajorCategoryBK;
use App\Models\Gk\MajorCategoryZK;
use App\Models\Gk\MajorPostDistribution;
use App\Models\Gk\MajorSubjectBK;
use App\Models\Gk\MajorSubjectZK;
use App\Models\Gk\MajorWorkingDistribution;
use App\Models\Gk\UserTestHollandMajor;
use App\Models\School\Xuezhi\MajorTraits;
use App\Models\Gk\OccupationAi;
use App\Repositories\MajorRepository;
use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use App\Helpers\NewGaokaoProvinces;
use App\Helpers\ProvinceHelper;
use Illuminate\Support\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Gk\MajorAiIntroduction;
use Illuminate\Support\Str;

class MajorService extends BaseService
{
    // 指定连接
    protected $connection = 'sqlsrv_gk';

    public function __construct(protected MajorRepository $majorRepository)
    {
    }
    public function category()
    {
        // 预加载关联数据
        $allSubject = [
            'BK' => MajorSubjectBK::with('categories.majors')->where('SubjectName', '!=', '其他')->get(),
            'ZK' =>  MajorSubjectZK::with('categories.majors')->get(),
        ];

        return $allSubject;
    }

    /**
     * 搜索专业，用于下拉专业列表
     * @param string $majorName 专业名称
     * @return array 搜索结果
     */
    public function searchByLinkName(string $majorName): array
    {
        // 获取所有专业数据
        $searches = $this->getAllMajorsForDynamicPullDown();

        $results = [];

        if (!empty($majorName)) {
            foreach ($searches as $search) {
                if (str_contains($search['major_name'], $majorName)) {
                    $result = [
                        'major_code' => $search['major_code'] ?? '',
                        'major_name' => $search['major_name'],
                        'phase' => $search['phase'],
                    ];
                    $results[] = $result;
                }
            }
        }

        return [
            'total' => count($results),
            'list' => $results,
        ];
    }

    /**
     * 获取所有专业用于动态下拉列表
     * @return array 专业列表
     */
    private function getAllMajorsForDynamicPullDown(): array
    {
        // 查询本科专业
        $bkMajors = DB::connection($this->connection)
            ->table('Major_BK')
            ->select(
                DB::raw("CONCAT(MajorName, '(本)') as major_name"),
                'MajorCode as major_code',
                DB::raw("'1' as phase")
            )
            ->get()
            ->toArray();

        // 查询专科专业
        $zkMajors = DB::connection($this->connection)
            ->table('Major_ZK')
            ->select(
                DB::raw("CONCAT(MajorName, '(专)') as major_name"),
                'MajorCode as major_code',
                DB::raw("'4' as phase")
            )
            ->get()
            ->toArray();

        // 合并结果并转换为数组
        $allMajors = array_merge($bkMajors, $zkMajors);

        // 转换为关联数组
        return array_map(function($item) {
            return (array) $item;
        }, $allMajors);
    }


    /**
     * 获取专业详情
     * @param string $major_code 专业代码
     * @return object|null 专业详情
     */
    public function info($major_code)
    {
        //根据$major_code 获取当前专业是本科专业还是专科专业
        $phase = MajorConstants::getMajorPhase($major_code);
        $viewTable = MajorConstants::getMajorViewTable($phase);

        $selectFields = $this->getInfoSelectFields($phase);
        $result = $this->queryMajorInfo($viewTable, $selectFields, $major_code, $phase);

        // 处理 AI 简介格式
        $this->formatAiIntroduction($result, $major_code);

        // 处理相关专业
        $this->processSimilarSpecialty($result);

        // 处理本科专业特有数据
        if ($phase == PhaseEnum::UNDERGRADUATE->value) {
            $this->processUndergraduateMajorData($result, $major_code);
        }

        // 处理 zymyd 字段
        $this->processZymydField($result);

        // 处理 kskc 字段
        $this->processKskcField($result);

        // 添加专业特质
        $result->zytz = $this->getMajorTrait($result->major_name);

        // 处理文理比例和性别比例
        $this->processRatioFields($result);

        // 处理职业详情，生成职业列表
        $this->processOccupationDetail($result);

        return $result;
    }

    public function getMajorTrait($majorName) {
        $majorTrait = MajorTraits::where('name', $majorName)->first();
        return $majorTrait;
    }

    /**
     * 获取排名前五的专业院校 top5MajorCollegeRank
     * @param MajorRequest $request 请求对象
     */
    public function top5MajorCollegeRank($major_code)
    {
        try {
            // 使用模型查询最新年份的专业排名数据
            return $this->majorRepository->getTop5MajorCollegeRank($major_code);
        } catch (\Exception $e) {
            Log::error('获取专业排名前五的院校失败: ' . $e->getMessage());
            return [];
        }
    }


     /**
     * 获取专业开设院校
     * @param MajorRequest $request 请求对象
     * @return Builder 查询构建器
     */
    public function colleges(MajorRequest $request)
    {
        //省份id
        $provinceId = $request['province_id'];
        //major_name
        $majorName = $request['major_name'];
        $liberalScience = $request['liberalScience'];
        $highestScore = $request['highest_score'];
        $lowestScore = $request['lowest_score'];
        $phases = $request['phases'];
        $stageName = $request['stageName'];
        $yxjbz = $request['yxjbz'];
        $collect = $request['collect'];
        $provinceIds = $request['provinceIds'];
        $year = $request['year'];
        $type = $request['type'] ?? 1;

        try {
            $provinceSpell = ProvinceEnum::fromInt($provinceId)->getPinyin();
            $provinceName = ProvinceEnum::fromInt($provinceId)->getDisplayName();

            // 确定查询表和最大年份
            $queryTable = "MajorScore_$provinceSpell";
            $yearTable = $this->determineYearTable($provinceName, $provinceSpell);
            // 如果$year不为空，使用$year作为年份 否则使用$yearTable作为年份
            $maxYear = $year ? $year : $this->getMaxYear($yearTable);
            // $maxYear = $this->getMaxYear($yearTable);

            // 构建基础查询，传递省份信息用于处理浙江省特殊字段
            $query = $this->buildCollegesBaseQuery($queryTable, $majorName, $maxYear, $type, $provinceSpell);

            // 应用过滤条件
            $this->applyCollegesFilters($query, $provinceSpell, $request, $liberalScience, $highestScore,
                $lowestScore, $phases, $stageName, $yxjbz, $collect, $provinceIds);

            // 设置排序
            if ($type == 1) {
                $query->orderByRaw('ISNULL(mr.Rank, 999999), mr.Rank ASC, MAX(amq.lowestScore) DESC');
            }else {
                $query->orderByRaw('ISNULL(mr.Rank, 999999), mr.Rank ASC, amq.lowestScore DESC');
            }

            return $query;
        } catch (\Exception $e) {
            throw new BusinessException("获取专业开设院校失败", 500, $e->getMessage());
        }
    }


    /**
     * 给开设院校设置tags
     */
    public function setTags(Collection $list): Collection
    {
        try {
            // 获取所有 college_id
            $collegeIds = $list->pluck('college_id')->unique()->toArray();

            if (empty($collegeIds)) {
                return $list;
            }

            // 查询所有对应的 College 模型，带 tagsFiltered 的 Type 字段
            $collegesWithTags = College::with([
                'tagsFiltered' => function ($query) {
                    $query->select('CollegeId', 'Type'); // 只查 CollegeId 和 Type 字段
                }
            ])->whereIn('ID', $collegeIds)->get()->keyBy('ID');

            // 整合 tagsFiltered Type 数据到 $list
            return $list->transform(function ($item) use ($collegesWithTags) {
                if (isset($collegesWithTags[$item->college_id])) {
                    $tags = $collegesWithTags[$item->college_id]->tagsFiltered ?? collect();
                    $item->tags_filtered = $tags->pluck('Type')->toArray();
                } else {
                    $item->tags_filtered = [];
                }
                return $item;
            });
        } catch (\Exception $e) {
            Log::error('设置院校标签失败: ' . $e->getMessage());
            return $list;
        }
    }



    /**
     * 获取专业开设院校数量
     */
    public function majorAcademicGroupList()
    {
        $list = MajorAcademicGroupBK::all();

        foreach ($list as $bean) {
            // 获取专业类目（类似于 getMajorSubjectByAcademicGroupId 方法）
            $majorSubjects = DB::connection($this->connection)->table('View_Major_BK_Summarizing as vm')
                ->join('Major_BK as m', 'vm.MajorID', '=', 'm.ID')
                ->where('m.AcademicGroupId', 'like', '%' . $bean->Id . '%')
                ->groupBy('vm.SubjectCode', 'vm.SubjectName', 'm.MajorSubjectID')
                ->get(['vm.SubjectCode', 'vm.SubjectName', 'm.MajorSubjectID'])
                ->map(function ($item) {
                    return (array)$item;
                })
                ->toArray();

            if (!empty($majorSubjects)) {
                foreach ($majorSubjects as &$subject) {
                    $subject['Category'] = $this->getMajorCategoryList(1, $subject['MajorSubjectID']);
                }
            }

            $bean->major_subjects = $majorSubjects;
        }

        return $list;
    }

    /**
     * 学职群视频列表查询
     * - 来自 MajorAcademicGroupCourse 表，联表 Major_BK
     * - 仅取 State=1 且 MajorID 不为空的数据
     * - 可按学职群ID模糊过滤（m.AcademicGroupId like %academic_group_id%）
     */
    public function academicGroupVideosQuery(MajorRequest $request): Builder
    {
        $academicGroupId = $request->input('academic_group_id') ?? $request->input('AcademicGroupId');

        $query = MajorAcademicGroupCourse::query()->from('MajorAcademicGroupCourse as ma')
            ->join('Major_BK as m', 'ma.MajorID', '=', 'm.ID')
            ->leftJoin('Province as p', 'p.ID', '=', 'ma.ProvinceID')
            ->leftJoin('College as c', 'c.ID', '=', 'ma.CollegeID')
            ->where('ma.State', 1)
            ->whereNotNull('ma.MajorID');

        if (!empty($academicGroupId)) {
            $query->where('m.AcademicGroupId', 'like', "%{$academicGroupId}%");
        }

        return $query->select([
            'ma.ID as id',
            'ma.CourseName as course_name',
            'ma.Tag as tag',
            'ma.PlayListID as play_list_id',
            'ma.CourseContent as course_content',
            'ma.CoursePhoto as course_photo',
            'ma.PlayTotal as play_total',
            'ma.Click as click',
            'ma.UPClick as up_click',
            'p.ProvinceName as province_name',
            'c.CollegeName as college_name',
            'm.MajorName as major_name',
            'm.MajorCode as major_code',
        ])
            ->orderByDesc('ma.Click')
            ->orderByDesc('ma.PlayTotal')
            ->orderByDesc('ma.ID')
            ->getQuery();
    }



    private function getMajorCategoryList($phase, $majorSubjectID)
    {
        $categories = null;

        if ($phase == PhaseEnum::UNDERGRADUATE->value) {
            $categories = MajorCategoryBK::with('majors')
                ->where('MajorSubjectID', $majorSubjectID)
                ->where('MajorCategoryName', '!=', '其它')
                ->get();
        } elseif ($phase == PhaseEnum::COLLEGE->value) {
            $categories = MajorCategoryZK::with('majors')
                ->where('MajorSubjectID', $majorSubjectID)
                ->get();
        }

        return $categories;
    }

    /**
     * aiIntroduction
     */
    public function aiIntroduction($code): ?MajorAiIntroduction
    {
        // 查询数据
        $bean = MajorAiIntroduction::where('Code', $code)->first();

        if ($bean && !empty($bean->Introduction)) {
            // 如果是专业学科、小类，因为数据排版，加点换行
            if (Str::length($bean->Code) <= 4) {
                $introduction = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" .
                    str_replace(
                        ['。', '！'],
                        ['。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;', '！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'],
                        $bean->Introduction
                    );
                $bean->Introduction = $introduction;
            }
        }

        return $bean;
    }

    /**
     * employment
     */
    public function employment($major_code)
    {
        $employment = [];
        //获取专业详情
        $major_detail = $this->info($major_code);
        if (!empty($major_detail)) {
            $major_id = $major_detail->id;
            // 使用模型获取最新年份
            $latest_year = MajorPostDistribution::max('Year');

            // Get industry distribution
            $trades = MajorPostDistribution
                ::select('MajorName as major_name', 'SpecificPosition as specific_position', 'Industry as industry', 'Position as position', 'Proportin as proportion')
                ->where('MajorID', $major_id)
                ->where('Year', $latest_year)
                ->orderByDesc('Proportin')
                ->get()
                ->toArray();

            // Get city distribution
            $majorWorkCitys = MajorWorkingDistribution::select('ID as id', 'MajorName as major_name', 'CityName as city_name', 'Proportion as proportion')
                ->where('MajorID', $major_id)
                ->where('Year', $latest_year)
                ->orderByDesc('Proportion')
                ->limit(10)
                ->get()
                ->toArray();

            // Get salary data
            $salarys = MajorAverageMonthlySalary::select('ID as id', 'MajorName as major_name', 'WorkingAge as working_age', 'MonthlySalary as monthly_salary', 'NominalPrice as nominal_price')
                ->where('MajorID', $major_id)
                ->where('Year', $latest_year)
                ->orderBy('WorkingAge')
                ->get()
                ->toArray();

            // Populate the employment array
            $employment['salarys'] = $salarys;
            $employment['work_cities'] = $majorWorkCitys;
            $employment['trade'] = $trades;
        }

        return $employment;
    }


    /**
     * xuankeDistributed
     */
    public function xuankeDistributed($major_name, $province_id)
    {
        $provinceId = (int) $province_id; // Ensure integer type
        $provinceSpell = ProvinceEnum::fromInt($provinceId)->getPinyin();

        // 获取最大年份
        $planYear = DB::connection($this->connection)->table("MajorPlan_{$provinceSpell}")->max('Year');
        $xuankeYear = DB::connection($this->connection)->table("Xuanke_{$provinceSpell}")->max('Year');
        // 确定使用的年份和表名
        $year = $planYear;
        $tableName = "MajorPlan_{$provinceSpell}";

        // Adjust year and table if xuankeYear is greater than planYear and it's a new Gaokao province
        if ($planYear < $xuankeYear && NewGaokaoProvinces::isNewGaokaoProvince($provinceId)) {
            $year = $xuankeYear;
            $tableName = "Xuanke_{$provinceSpell}";
        }

        // Get score form type (e.g., 312-2021 or wl)
        $scoreFormType = ProvinceHelper::getScoreFormType2($provinceId);

        if (!str_contains($scoreFormType, 'wl')) {
            $hasCourse = 'one';
            if (str_contains($scoreFormType, '312')) {
                $hasCourse = 'two';
            }

            // 查询选科分布数据
            if (str_contains($tableName, 'Xuanke')) {
                $list = $this->xuankeDistributedByXuanKe($major_name, $tableName, $year, $hasCourse);
            } else {
                $list = $this->xuankeDistributedByPlan($major_name, $tableName, $year, $hasCourse);
            }

            // 计算总数
            $allNum = 0;
            foreach ($list as &$map) {
                $allNum += (int)$map->num;
            }

            // 计算百分比
            if ($allNum > 0) {
                foreach ($list as &$map) {
                    $percent = ($map->num / $allNum) * 100;
                    $map->percent = round($percent, 2) . '%';
                }
            }

            // 添加总数和年份到结果列表
            array_unshift($list, [
                'all_num' => $allNum,
                'data_year' => $year,
            ]);
        }

        return $list;
    }


    /**
     * 查询选科分布数据（Xuanke 表）
     *
     * @param string $major_name 专业名称
     * @param string $tableName 表名
     * @param int $year 年份
     * @param string $hasCourse 选科类型
     * @return array
     */
    private function xuankeDistributedByXuanKe($major_name, $tableName, $year, $hasCourse)
    {
        $query = DB::connection($this->connection)->table($tableName)
            ->select('Course as course')
            ->where('Year', $year)
            ->where(function ($query) use ($major_name) {
                $query->where('MajorName', 'like', "%{$major_name}%")
                    ->orWhere('MajorRemark', 'like', "%{$major_name}%");
            })
            ->groupBy('Course');

        if ($hasCourse === 'two') {
            $query->addSelect('CourseSecond as course_second')
                ->groupBy('CourseSecond');
        }

        return $query->selectRaw('COUNT(*) as num')
            ->orderByDesc('Num')
            ->get()
            ->toArray();
    }

    /**
     * 查询选科分布数据（MajorPlan 表）
     *
     * @param string $major_name 专业名称
     * @param string $tableName 表名
     * @param int $year 年份
     * @param string $hasCourse 选科类型
     * @return array
     */
    private function xuankeDistributedByPlan($major_name, $tableName, $year, $hasCourse)
    {
        $query = DB::connection($this->connection)->table($tableName)
            ->select('Course as course')
            ->where('Year', $year)
            ->where(function ($query) use ($major_name) {
                $query->where('Major', 'like', "%{$major_name}%")
                    ->orWhere('MajorRemark', 'like', "%{$major_name}%");
            })
            ->groupBy('Course');

        if ($hasCourse === 'two') {
            $query->addSelect('CourseSecond as course_second')
                ->groupBy('CourseSecond');
        }

        return $query->selectRaw('COUNT(*) as num')
            ->orderByDesc('Num')
            ->get()
            ->toArray();
    }


    /**
     * 获取学职群数据
     * @param string $major_code 专业代码
     * @param int $major_id 专业ID
     * @return array
     */
    public function academic($major_code, $major_id)
    {
        $result = [
            'academic_name' => null,
            'academics' => null,
        ];

        // 查询学术课程数据
        $list = $this->selectAcademics($major_code, $major_id);

        if (!empty($list)) {
            foreach ($list as &$map) {
                $videoEncrypt = $this->getVideoEncrypt($map->play_list_id);
                $map->video = $videoEncrypt;
            }
        }

        // 查询学术组ID
        $ids = $this->selectAcademicIds($major_code);

        if (!empty($ids)) {
            $idsArray = array_map('intval', explode(',', trim($ids)));
            $academicNames = $this->selectAcademicName($major_code, $idsArray);

            $result['academic_name'] = $academicNames;
            $result['academics'] = $list;
        }

        return $result;
    }

    /**
     * 查询学术课程数据
     *
     * @param string $major_code 专业代码
     * @param int $major_id 专业ID
     * @return array
     */
    private function selectAcademics($major_code, $major_id)
    {
        return DB::connection($this->connection)->table('MajorAcademicGroupCourse as m')
            ->select('m.ID as id', 'm.CourseName as course_name', 'm.Tag as tag', 'm.PlayListID as play_list_id', 'm.CourseContent as course_content', 'm.CoursePhoto as course_photo', 'p.ProvinceName as province_name', 'c.CollegeName as college_name')
            ->leftJoin('Province as p', 'p.ID', '=', 'm.ProvinceID')
            ->leftJoin('College as c', 'c.ID', '=', 'm.CollegeID')
            ->where('m.State', 1)
            ->whereIn('m.CategoryID', function ($query) use ($major_code) {
                $query->select('MajorCategoryID')
                    ->from('Major_Bk')
                    ->where('MajorCode', $major_code)
                    ->limit(1);
            })
            ->where(function ($query) use ($major_id) {
                $query->whereNull('m.MajorID')
                    ->orWhere('m.MajorID', '')
                    ->orWhere('m.MajorID', $major_id);
            })
            ->orderBy('p.ProvinceName', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 查询学术组ID
     *
     * @param string $major_code 专业代码
     * @return string
     */
    private function selectAcademicIds($major_code)
    {
        return DB::connection($this->connection)->table('Major_Bk')
            ->where('MajorCode', $major_code)
            ->value('AcademicGroupId');
    }

    /**
     * 查询学术组名称
     *
     * @param string $major_code 专业代码
     * @param array $ids 学术组ID数组
     * @return array
     */
    private function selectAcademicName($major_code, $ids)
    {
        return DB::connection($this->connection)->table('MajorAcademicGroup_Bk as magb')
            ->select('magb.AcademicGroupName')
            ->leftJoin('Major_Bk as mb', function ($join) use ($ids) {
                $join->whereIn('magb.ID', $ids);
            })
            ->where('mb.MajorCode', $major_code)
            ->pluck('AcademicGroupName')
            ->toArray();
    }

    /**
     * 获取视频加密结果
     *
     * @param string $video_id 视频ID
     * @return array
     */
    private function getVideoEncrypt($video_id)
    {
        $secretkey = 'hcTDd2tF9j';
        $ts = now()->timestamp;
        $sign = md5($secretkey . $video_id . $ts);

        return [
            'sign' => $sign,
            'ts' => $ts,
        ];
    }



          /**
     * 获取专业信息查询的字段列表
     * @param int $phase 阶段（1:本科，4:专科）
     * @return array 字段列表
     */
    private function getInfoSelectFields(int $phase): array
    {
        $selectFields = [
            DB::connection($this->connection)->raw('COALESCE(m.ID, 0) as id'),
            'vm.Remark as remark',
            'vm.MajorSubjectID as major_subject_id',
            'vm.SubjectName as subject_name',
            'vm.MajorCategoryID as major_category_id',
            'vm.MajorCategoryName as major_category_name',
            'vm.MajorID as major_id',
            'vm.MajorName as major_name',
            'vm.MajorCode as major_code',
            'm.Phase as phase',
//            'm.Introduction as introduction',
//            'm.ClickRate as click_rate',
//            'm.OccupationalDirection as occupation_direction',
            'm.OccupationDetail as occupation_detail',
//            'm.SimilarSpecialty as similar_specialty',
//            'm.EmploymentDistribution as employment_distribution',
//            'm.OccupationDetailExt as occupation_detail_ext',
            'm.DegreeCategories as degree_categories',
            'm.LengthOfSchooling as length_of_schooling',
            'm.AddYear as add_year',
//            'ma.MonthlySalary as monthly_salary',
            'adv.Employment as employment',
            'adv.Exam as exam',
            'bio.WLRatio as wl_ratio',
//            'bio.SexRatio as sex_ratio',
//            'bio.Graduates as graduates',
            'm.MasterFocus as master_focus',
            'mai.Introduction as ai_introduction',
            'mai.Course as ai_course',
            'mai.JobDirection as ai_job_direction',
        ];

        if ($phase == PhaseEnum::UNDERGRADUATE->value) {
            $selectFields[] = DB::connection($this->connection)->raw("REPLACE(vm.AcademicGroupID, ' ', '') as academic_group_id");
        }

        return $selectFields;
    }

        /**
     * 查询专业详细信息
     * @param string $viewTable 视图表名
     * @param array $selectFields 查询字段
     * @param string $major_code 专业代码
     * @param int $phase 阶段
     * @return object|null 查询结果
     */
    private function queryMajorInfo(string $viewTable, array $selectFields, string $major_code, int $phase)
    {
        return DB::connection($this->connection)->table("{$viewTable} as vm")
            ->leftJoin('Major as m', function ($join) use ($phase) {
                $join->on('vm.MajorCode', '=', 'm.MajorCodegb')
                    ->where('m.Phase', '=', $phase);
            })
//            ->leftJoin('Major_AverageMonthlySalary as ma', function ($join) {
//                $join->on('m.ID', '=', 'ma.MajorID')
//                    ->where('ma.WorkingAge', '=', MajorConstants::DEFAULT_WORKING_AGE)
//                    ->where('ma.Year', '=', MajorConstants::DEFAULT_SALARY_YEAR);
//            })
            ->leftJoin('MajorAdvise as adv', 'm.MajorName', '=', 'adv.Name')
            ->leftJoin('MajorAiIntroduction as mai', 'vm.MajorCode', '=', 'mai.Code')
            ->leftJoin('MajorBiogenesis as bio', function ($join) use ($phase) {
                $education = $phase == PhaseEnum::UNDERGRADUATE->value ? '本科' : '专科';
                $join->on('m.MajorName', '=', 'bio.Name')
                    ->where('bio.Xueli', '=', $education);
            })
            ->leftJoin($phase == PhaseEnum::UNDERGRADUATE->value ? 'Major_BK as mbk' : 'Major_ZK as mzk', function ($join) use ($major_code, $phase) {
                $join->on('m.MajorName', '=', $phase == PhaseEnum::UNDERGRADUATE->value ? 'mbk.MajorName' : 'mzk.MajorName')
                    ->where($phase == PhaseEnum::UNDERGRADUATE->value ? 'mbk.MajorCode' : 'mzk.MajorCode', '=', $major_code);
            })
            ->select($selectFields)
            ->where('vm.MajorCode', $major_code)
            ->first();
    }

        /**
     * 格式化AI简介
     * @param object|null $result 专业信息结果
     * @param string $major_code 专业代码
     */
    private function formatAiIntroduction(?object $result, string $major_code): void
    {
        if ($result && isset($result->ai_introduction)){
            if (strlen($major_code) <= 4) {
                $result->ai_introduction = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" .
                    str_replace(["。", "！"], ["。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;", "！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"], $result->ai_introduction);
            }
        }
    }

    /**
     * 处理相关专业信息
     * @param object|null $result 专业信息结果
     */
    private function processSimilarSpecialty(?object $result): void
    {
        if (!empty($result->similar_specialty)) {
            $similarSpecialty = str_replace('，', ',', $result->similar_specialty);
            $majorNames = explode(',', $similarSpecialty);
            $nearMajor = [];

            foreach ($majorNames as $majorName) {
                $selectPhase = DB::connection($this->connection)->table('Major')
                    ->where('MajorName', $majorName)
                    ->value('Phase') ?? PhaseEnum::UNDERGRADUATE->value;

                $majorCode1 = DB::connection($this->connection)->table($selectPhase == PhaseEnum::UNDERGRADUATE->value ? 'Major_BK' : 'Major_ZK')
                    ->where('MajorName', $majorName)
                    ->value('MajorCode');

                $nearMajor[] = [
                    'major_name' => $majorName,
                    'major_code' => $majorCode1 ?? '',
                ];
            }

            $result->near_major = $nearMajor;
        }
    }

    /**
     * 处理本科专业特有数据
     * @param object|null $result 专业信息结果
     * @param string $major_code 专业代码
     */
    private function processUndergraduateMajorData(?object $result, string $major_code): void
    {
        $majorYggkBean = DB::connection($this->connection)
            ->table(DB::raw("(SELECT CAST(Detail AS NVARCHAR(MAX)) COLLATE Chinese_PRC_CI_AS AS Detail, CAST(Kskc AS NVARCHAR(MAX)) COLLATE Chinese_PRC_CI_AS AS Kskc,MajorCode,PhaseType, MajorName,Zyjd FROM MajorYggk) AS temp_table"))
            ->where('PhaseType', '本科')
            ->where('MajorCode', $major_code)
            ->first();

        if ($majorYggkBean) {
            $detail = json_decode($majorYggkBean->Detail, true);
            $result->zymyd = $detail['zymyd'] ?? null;
            $result->xsgm = $detail['xsgm'] ?? null;
            $result->boy_percent = $detail['boyPercent'] ?? null;
            $result->girl_percent = $detail['girlPercent'] ?? null;
            $result->kskc = json_decode($majorYggkBean->Kskc, true)?? null;
            if (!empty($detail['simileZyList'])) {
                $this->processSimileZyList($result, $detail['simileZyList']);
            }
        }
    }

    /**
     * 处理相似专业列表
     * @param object $result 专业信息结果
     * @param array $simileZyList 相似专业列表
     */
    private function processSimileZyList(?object $result, array $simileZyList): void
    {
        $nearMajor = [];
        $majors = '';

        foreach ($simileZyList as $map2) {
            $majorName = $map2['zymc'];
            $selectPhase = DB::connection($this->connection)->table('Major')
                ->where('MajorName', $majorName)
                ->value('Phase') ?? PhaseEnum::UNDERGRADUATE->value;

            $majorCode1 = DB::connection($this->connection)->table($selectPhase == PhaseEnum::UNDERGRADUATE->value ? 'Major_BK' : 'Major_ZK')
                ->where('MajorName', $majorName)
                ->value('MajorCode');

            $nearMajor[] = [
                'major_name' => $majorName,
                'major_code' => $majorCode1 ?? '',
            ];

            $majors .= $majorName . ',';
        }

        if (!empty($nearMajor)) {
            $result->near_major = $nearMajor;
            $result->similar_specialty = substr($majors, 0, -1);
        }
    }

    /**
     * 处理专业满意度字段
     * @param object|null $result 专业信息结果
     */
    private function processZymydField(?object $result): void
    {
        if (!empty($result->zymyd)) {
            foreach ($result->zymyd as &$item) {
                $item['type_desc'] = $item['typeDesc'];
                unset($item['typeDesc']);
            }
        }
    }

     /**
     * 处理开设课程字段
     * @param object|null $result 专业信息结果
     */
    private function processKskcField(?object $result): void
    {
        if (!empty($result->kskc)) {
            try {
                foreach ($result->kskc as &$course) {
                    $course['practicality_total'] = $course['praTotal'];
                    unset($course['praTotal']);
                    $course['difficulty_total'] = $course['diffTotal'];
                    unset($course['diffTotal']);
                }
            } catch (\Exception $e) {
                Log::error('KSKC数据处理失败', [
                    'error' => $e->getMessage()
                ]);
                $result->kskc = [];
            }
        } else {
            Log::error('KSKC数据为NULL');
            $result->kskc = [];
        }
    }

    /**
     * 处理比例字段（文理比例和性别比例）
     * @param object|null $result 专业信息结果
     */
    private function processRatioFields(?object $result): void
    {
        // 处理文理比例
        if (!empty($result->wl_ratio)) {
            $wlRatioData = json_decode($result->wl_ratio, true);
            if (isset($wlRatioData['list'])) {
                $result->wl_ratio = $this->extractRatioValues($wlRatioData['list']);
            }
        }

        // 处理性别比例
        if (!empty($result->sex_ratio)) {
            $sexRatioData = json_decode($result->sex_ratio, true);
            if (isset($sexRatioData['list'])) {
                $result->sex_ratio = $this->extractRatioValues($sexRatioData['list']);
            }
        }
    }

    /**
     * 从比例数据中提取值
     * @param array $ratioList 比例数据列表
     * @return array 提取后的比例值
     */
    private function extractRatioValues(array $ratioList): array
    {
        $newRatio = [];
        foreach ($ratioList as $item) {
            $newRatio[$item['name']] = $item['value'];
        }
        return $newRatio;
    }


    /**
     * 确定年份表
     * @param string $provinceName 省份名称
     * @param string $provinceSpell 省份拼音
     * @return string 表名
     */
    private function determineYearTable(string $provinceName, string $provinceSpell): string
    {
        if (BaseConstants::hasNoScoreDataTable($provinceName)) {
            return "SiteYear";
        }

        return "MajorScore_{$provinceSpell}";
    }

    /**
     * 获取最大年份
     * @param string $tableName 表名
     * @return int 最大年份
     */
    private function getMaxYear(string $tableName): int
    {
        return DB::connection($this->connection)->table($tableName)->max('Year') ?? date('Y');
    }

    /**
     * 构建专业开设院校基础查询
     * @param string $queryTable 查询表名
     * @param string $majorName 专业名称
     * @param int $maxYear 最大年份
     * @param int $type 查询类型
     * @param string $provinceSpell 省份拼音
     * @return Builder 查询构建器
     */
    private function buildCollegesBaseQuery(string $queryTable, string $majorName, int $maxYear, int $type, string $provinceSpell = ''): Builder
    {
        $builder = DB::connection($this->connection)->table($queryTable . ' as amq')
            ->join('college as c', 'c.id', '=', 'amq.collegeid')
            ->leftJoin('province as p', 'c.ProvinceID', '=', 'p.ID')
            ->leftJoin('city as cy', 'c.CityID', '=', 'cy.ID')
            ->leftJoin('MajorRank as mr', function($join) use ($majorName) {
                $join->on('c.id', '=', 'mr.CollegeId')
                     ->where('mr.Year', MajorConstants::MAJOR_RANK_YEAR)
                     ->where('mr.MajorName', '=', $majorName);
            })
            ->leftJoin(DB::connection($this->connection)->raw('(
                SELECT ma1.CollegeId, ma1.Major, ma1.Rank, ma1.Num as max_num
                FROM MajorAssess ma1
                INNER JOIN (
                    SELECT CollegeId, Major, MAX(Num) as max_num
                    FROM MajorAssess
                    GROUP BY CollegeId, Major
                ) ma2 ON ma1.CollegeId = ma2.CollegeId
                    AND ma1.Major = ma2.Major
                    AND ma1.Num = ma2.max_num
            ) as ma'), function($join) use ($majorName) {
                $join->on('c.id', '=', 'ma.CollegeId')
                     ->where('ma.Major', '=', $majorName);
            })
            ->where('amq.Year', $maxYear);
            // 根据类型应用不同的查询字段和分组
        return $type == 1
        ? $this->applyGroupedQuery($builder, $provinceSpell)
        : $this->applyNormalQuery($builder, $provinceSpell);
    }

    /**
     * 应用分组查询
     * @param Builder $builder 查询构建器
     * @param string $provinceSpell 省份拼音
     * @return Builder 处理后的查询构建器
     */
    private function applyGroupedQuery(Builder $builder, string $provinceSpell = ''): Builder
    {
        // 基础字段
        $baseFields = [
            'c.ID as college_id',
            'c.collegeName as college_name',
            'c.provinceID as province_id',
            'c.cityID as city_id',
            'c.Yxls as yxls',
            'c.Yxtype as yxtype',
            'c.Yxjbz as yxjbz',
            'c.BigLogo as big_logo',
            'c.SmallLogo as small_logo',
            'c.state',
            'amq.major',
            'p.ProvinceName as province_name',
            'cy.CityName as city_name',
            'mr.Rank as major_rank',
            'mr.CollegeLevel as college_level',
            'ma.Rank as major_assess',
            'ma.max_num as max_num'
        ];

        // 聚合字段
        $aggregateFields = [
            'MAX(amq.liberalScience) as liberalScience',
            'MAX(amq.phase) as phase',
            'MAX(amq.phaseName) as phase_name',
            'MAX(amq.highestScore) as highest_score',
            'MAX(amq.highestScoreRank) as highest_score_rank',
            'MAX(amq.avgScore) as avg_score',
            'MAX(amq.avgScoreRank) as avg_score_rank',
            'MAX(amq.lowestScore) as lowest_score',
            'MAX(amq.lowestScoreRank) as lowest_score_rank',
            'MAX(amq.majorRemark) as major_remark'
        ];

        // GROUP BY 字段
        $groupByFields = [
            'c.ID',
            'c.collegeName',
            'c.provinceID',
            'c.cityID',
            'c.Yxls',
            'c.Yxtype',
            'c.Yxjbz',
            'c.BigLogo',
            'c.SmallLogo',
            'c.state',
            'amq.major',
            'p.ProvinceName',
            'cy.CityName',
            'mr.Rank',
            'mr.CollegeLevel',
            'ma.Rank',
            'ma.max_num'
        ];

        // 浙江省特殊字段处理
        if ($provinceSpell === 'zhejiang') {
            $baseFields[] = 'amq.stageName as stage_Name';
            $baseFields[] = 'amq.planCount as plan_count';
            $groupByFields[] = 'amq.stageName';
            $groupByFields[] = 'amq.planCount';
        }

        return $builder->select(array_merge($baseFields, [DB::raw(implode(', ', $aggregateFields))]))
            ->groupBy($groupByFields);
    }

    /**
     * 应用普通查询
     * @param Builder $builder 查询构建器
     * @param string $provinceSpell 省份拼音
     * @return Builder 处理后的查询构建器
     */
    private function applyNormalQuery(Builder $builder, string $provinceSpell = ''): Builder
    {
        $selectFields = [
            'c.ID as college_id',
            'c.collegeName as college_name',
            'c.provinceID as province_id',
            'c.cityID as city_id',
            'c.Yxls as yxls',
            'c.Yxtype as yxtype',
            'c.Yxjbz as yxjbz',
            'c.BigLogo as big_logo',
            'c.SmallLogo as small_logo',
            'c.state',
            'amq.major',
            'amq.liberalScience',
            'amq.phase',
            'amq.phaseName as phase_name',
            'amq.highestScoreRank as highest_score_rank',
            'amq.avgScoreRank as avg_score_rank',
            'amq.lowestScoreRank as lowest_score_rank',
            'amq.highestScore as highest_score',
            'amq.avgScore as avg_score',
            'amq.lowestScore as lowest_score',
            'amq.majorRemark as major_remark',
            'p.ProvinceName as province_name',
            'cy.CityName as city_name',
            'mr.Rank as major_rank',
            'mr.CollegeLevel as college_level',
            'ma.Rank as major_assess',
            'ma.max_num as max_num'
        ];

        // 浙江省特殊字段处理
        if ($provinceSpell === 'zhejiang') {
            $selectFields[] = 'amq.stageName as stage_Name';
            $selectFields[] = 'amq.planCount as plan_count';
        }

        return $builder->select($selectFields);
    }

    /**
     * 应用专业开设院校过滤条件
     * @param Builder $query 查询构建器
     * @param string $provinceSpell 省份拼音
     * @param MajorRequest $request 请求对象
     * @param string|null $liberalScience 文理科
     * @param int|null $highestScore 最高分
     * @param int|null $lowestScore 最低分
     * @param string|null $phases 阶段
     * @param string|null $stageName 阶段名称
     * @param string|null $yxjbz 院校级别组
     * @param array|null $collect 收集
     * @param string|null $provinceIds 省份IDs
     */
    private function applyCollegesFilters(Builder $query, string $provinceSpell, MajorRequest $request,
        ?string $liberalScience, ?int $highestScore, ?int $lowestScore, ?string $phases,
        ?string $stageName, ?string $yxjbz, ?array $collect, ?string $provinceIds): void
    {
        // 应用专业名称过滤
        $query->where('amq.major', 'like', "%{$request['major_name']}%");

        // 文理科过滤
        if ($provinceSpell != 'zhejiang' && $request->filled('liberalScience')) {
            $query->whereIn('amq.liberalScience', explode(',', $liberalScience));
        }

        // 分数过滤
        if ($request->filled('highestScore')) {
            $query->where('amq.lowestScore', '<=', $highestScore);
        }

        if ($request->filled('lowestScore')) {
            $query->where('amq.lowestScore', '>=', $lowestScore);
        }

        // 阶段过滤
        if ($provinceSpell != 'zhejiang' && $request->filled('phases')) {
            $query->whereIn('amq.phase', explode(',', $phases));
        }

        // 浙江省特殊过滤
        if ($provinceSpell == 'zhejiang' && $request->filled('stageName')) {
            $query->whereIn('amq.stageName', explode(',', $stageName));
        }

        // 院校特性过滤
        $this->applyCollegeFeatureFilters($query, $request, $yxjbz, $collect, $provinceIds);
    }

    /**
     * 应用院校特性过滤
     * @param Builder $query 查询构建器
     * @param MajorRequest $request 请求对象
     * @param string|null $yxjbz 院校级别组
     * @param array|null $collect 收集
     * @param string|null $provinceIds 省份IDs
     */
    private function applyCollegeFeatureFilters(Builder $query, MajorRequest $request,
        ?string $yxjbz, ?array $collect, ?string $provinceIds): void
    {
        if ($request->filled('is211')) {
            $query->where('c.Is211', 1);
        }

        if ($request->filled('is985')) {
            $query->where('c.Is985', 1);
        }

        if ($request->filled('yan')) {
            $query->where('c.IsYan', 1);
        }

        if ($request->filled('yxls')) {
            $query->where('c.Yxls', '教育部');
        }

        if ($request->filled('yxjbz')) {
            $query->whereIn('c.Yxjbz', explode(',', $yxjbz));
        }

        if ($request->filled('collect')) {
            $query->whereIn('c.Yxtype', $collect);
        }

        if ($request->filled('provinceIds')) {
            $query->whereIn('p.ID', explode(',', $provinceIds));
        }
    }


    /**
     * getToken
     */
    public function getToken($vid, $ts)
    {
        $userId = "748ee24631";
        $secretkey = "hcTDd2tF9j";
        $viewerId = $this->generateRandomNumbers(10);

        $map = [
            'userId' => $userId,
            'viewerId' => $viewerId,
            'videoId' => $vid,
            'ts' => (string)$ts
        ];

        $value = $secretkey . "ts" . $ts . "userId" . $userId . "videoId" . $vid . "viewerId" . $viewerId . $secretkey;
        // 使用与Java版本getMD5UpperCase方法相同的实现
        $sign = strtoupper(md5($value));
        $map['sign'] = $sign;

        try {
            // 修改为与Java代码相同的请求方式
            $response = Http::withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded;charset=utf-8'
            ])->asForm()->post("https://hls.videocc.net/service/v1/token", $map);
            $responseData = $response->json();

            if (isset($responseData['data']) && isset($responseData['data']['token'])) {
                return $responseData['data']['token'];
            }
            throw new BusinessException('获取视频token异常', 500);
        } catch (\Exception $e) {
            throw new BusinessException("获取视频token异常", 500, $e->getMessage());
        }
    }


    /**
     * 生成指定长度的随机数字
     *
     * @param int $length
     * @return string
     */
    private function generateRandomNumbers(int $length): string
    {
        // return Str::random($length, '0123456789');
        $numbers = '0123456789';
        return substr(str_shuffle($numbers), 0, $length);
    }


    /**
     * analysis
     */
    /**
     * 获取专业的荷兰码特征分析
     * @param string $major_code 专业代码
     * @return array 专业荷兰码特征分析结果
     */
    public function analysis($major_code)
    {
        try {
            // 查询男性荷兰码特征
            $maleCharacter = UserTestHollandMajor::select('HollandCode')
                ->where('MajorCode', $major_code)
                ->where('Gender', 1)
                ->pluck('HollandCode')
                ->toArray();

            // 查询女性荷兰码特征
            $femaleCharacter = UserTestHollandMajor::select('HollandCode')
                ->where('MajorCode', $major_code)
                ->where('Gender', 0)
                ->pluck('HollandCode')
                ->toArray();

            // 返回结果
            return [
                'maleCharacter' => $maleCharacter,
                'femaleCharacter' => $femaleCharacter
            ];
        } catch (\Exception $e) {
            Log::error('获取专业荷兰码特征分析失败: ' . $e->getMessage());
            return [
                'maleCharacter' => [],
                'femaleCharacter' => []
            ];
        }
    }

    /**
     * 处理职业详情字段，生成职业列表
     * @param object|null $result 专业信息结果
     */
    private function processOccupationDetail(?object $result): void
    {
        if (empty($result->occupation_detail)) {
            $result->occupation_list = [];
            return;
        }

        try {
            // 将 occupation_detail 转换为数组
            $occupationNames = explode('、', $result->occupation_detail);
            $occupationList = [];

            foreach ($occupationNames as $originalName) {
                $originalName = trim($originalName);
                if (empty($originalName)) {
                    continue;
                }

                // 去除括号内容，只保留括号外的数据用于查询
                $searchName = preg_replace('/\([^)]*\)/', '', $originalName);
                $searchName = trim($searchName);

                // 如果去除括号后为空，则使用原始名称
                if (empty($searchName)) {
                    $searchName = $originalName;
                }

                // 在职业库中模糊查询，获取第一条数据
                $occupation = OccupationAi::where('OccupationName', 'like', "%{$searchName}%")
                    ->where('State', 1)
                    ->first();

                $occupationList[] = [
                    'occupation_name' => $originalName,
                    'occupation_id' => $occupation ? $occupation->Id : null
                ];
            }

            $result->occupation_list = $occupationList;
        } catch (\Exception $e) {
            Log::error('处理职业详情失败: ' . $e->getMessage());
            $result->occupation_list = [];
        }
    }
}
