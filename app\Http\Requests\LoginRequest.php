<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class LoginRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'string',
                'min:4',
                Rule::exists('users', 'username')
            ],
            // 'password' => 'required|string|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/',
            'captcha' => 'required|string',
            'key' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '用户名不能为空',
            'username.min' => '用户名长度至少4位',
            'username.exists' => '账号错误',
            'password' => '密码长度至少8位,必须包含大写字母、小写字母、数字',
            'captcha' => '验证码不能为空',
            'key' => 'key 不能为空',
        ];
    }

    /**
     * 重写验证失败的处理方法，实现自定义错误码
     */
    protected function failedValidation(Validator $validator)
    {
        $errors = $validator->errors();
        $firstError = $errors->first();

        // 根据不同的验证错误返回不同的错误码
        $code = 400; // 默认错误码

        // 检查用户名存在性验证失败
        if ($errors->has('username') && str_contains($firstError, '账号错误')) {
            $code = 501; // 用户名不存在
        }
        // // 检查密码格式验证失败
        // elseif ($errors->has('password') && str_contains($firstError, '密码长度至少8位')) {
        //     $code = 502; // 密码格式不符合规范
        // }

        throw new HttpResponseException(response()->json([
            'code' => $code,
            'message' => $firstError,
        ], 200));
    }
}
