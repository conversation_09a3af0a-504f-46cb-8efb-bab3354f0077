<?php

namespace App\Services\Admin;

use App\Models\StudentFindpass;
use App\Models\StudentFindpassSmslog;
use App\Models\User;
use App\Services\BaseService;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\DB;

class StudentFindpassService extends BaseService
{
    /**
     * 获取找回密码申请列表
     */
    public function getList($request)
    {
        $query = StudentFindpass::query();

        // 搜索条件
        if ($request->filled('student_name')) {
            $query->where('student_name', 'like', '%' . $request->input('student_name') . '%');
        }

        if ($request->filled('user_name')) {
            $query->where('user_name', 'like', '%' . $request->input('user_name') . '%');
        }

        if ($request->filled('school_name')) {
            $query->where('school_name', 'like', '%' . $request->input('school_name') . '%');
        }

        if ($request->filled('check_status')) {
            $query->where('check_status', $request->input('check_status'));
        }

        // 时间范围搜索
        if ($request->filled('start_date')) {
            $query->whereDate('create_time', '>=', $request->input('start_date'));
        }

        if ($request->filled('end_date')) {
            $query->whereDate('create_time', '<=', $request->input('end_date'));
        }

        return $query;
    }

    /**
     * 处理找回密码申请
     * 管理员审核申请，可以通过或拒绝
     */
    public function processApplication($id, $adminUserId, $checkStatus, $request)
    {
        DB::beginTransaction();

        try {
            // 查找申请记录
            $findpassRecord = StudentFindpass::find($id);
            if (!$findpassRecord) {
                throw new BusinessException('申请记录不存在', 404);
            }

            // 检查是否已处理（只有状态为0的才能处理）
            if ($findpassRecord->check_status != 0) {
                throw new BusinessException('该申请已处理，无法重复操作', 400);
            }

            // 更新申请状态
            $findpassRecord->check_status = $checkStatus;
            $findpassRecord->check_time = now();
            $findpassRecord->save();

            $operationDesc = '';
            $message = '';

            if ($checkStatus == 1) {
                // 通过申请，更新用户密码
                $user = User::where('username', $findpassRecord->user_name)->first();

                if ($user) {
                    $user->password = $findpassRecord->password; // 使用申请中已加密的密码
                    $user->md5_password = $findpassRecord->md5_password; // 同步MD5密码
                    $user->save();

                    // 同时更新同步数据库中的 ysy_member 表
                    $this->updateYsyMemberPassword($findpassRecord->user_name, $findpassRecord->md5_password);
                }

                $operationDesc = '管理员通过找回密码申请，已更新用户密码';
                $message = '申请已通过，用户密码已更新';
            } else {
                // 拒绝申请
                $operationDesc = '管理员拒绝找回密码申请';
                $message = '申请已拒绝';
            }

            // 记录操作日志
            $this->createOperationLog(
                $findpassRecord->id,
                $adminUserId,
                $checkStatus == 1 ? 'approve' : 'reject',
                $operationDesc,
                $request
            );

            DB::commit();

            return [
                'message' => $message,
                'check_status' => $checkStatus,
                'status_text' => $this->getStatusText($checkStatus),
                'processed_at' => now()->format('Y-m-d H:i:s')
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        return match($status) {
            0 => '待处理',
            1 => '通过',
            -1 => '拒绝',
            default => '未知状态'
        };
    }

    /**
     * 更新同步数据库中 ysy_member 表的密码
     *
     * @param string $username 用户名
     * @param string $md5Password MD5密码
     * @return void
     */
    private function updateYsyMemberPassword(string $username, string $md5Password): void
    {
        try {
            // 使用同步数据库连接更新 ysy_member 表
            DB::connection('sync_mysql')->table('ysy_member')
                ->where('username', $username)
                ->update(['password' => $md5Password]);

            \Log::info('更新同步数据库 ysy_member 密码成功', [
                'username' => $username,
                'connection' => 'sync_mysql'
            ]);
        } catch (\Exception $e) {
            \Log::error('更新同步数据库 ysy_member 密码失败', [
                'username' => $username,
                'connection' => 'sync_mysql',
                'error' => $e->getMessage()
            ]);
            // 不抛出异常，避免影响主流程
        }
    }



    /**
     * 创建操作日志
     */
    private function createOperationLog($findpassId, $adminUserId, $operationType, $operationDesc, $request)
    {
        StudentFindpassSmslog::create([
            'student_findpass_id' => $findpassId,
            'admin_id' => $adminUserId,
            'create_time' => now(),
            'ip_address' => $request->ip(),
        ]);
    }


}
