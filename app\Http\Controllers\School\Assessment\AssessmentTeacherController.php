<?php

namespace App\Http\Controllers\School\Assessment;

use App\Enums\SystemRoleTypeEnum;
use App\Http\Controllers\Controller;
use App\Services\School\Assessment\AssessmentBasicService;
use App\Services\School\Assessment\AssessmentTeacherService;
use Illuminate\Http\Request;

class AssessmentTeacherController extends Controller
{


    public function __construct(protected AssessmentBasicService $assessmentBasicService, protected AssessmentTeacherService $assessmentTeacherService)
    {
        $this->assessmentBasicService = $assessmentBasicService;
        $this->assessmentTeacherService = $assessmentTeacherService;
    }
    
    /**
     * 获取测评列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function types()
    {

        $result = $this->assessmentBasicService->getAssessmentTypes();
        
        return $this->success($result);
    }

    /**
     * 添加计划时获取测评分类列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function assessmentList(Request $request)
    {
        $schoolId = $request->user()->organization->model_id;
        $type = $request->input('type'); // 不再设置默认值
        $user_id = $request->user()->id;
        
        //教务全展示，教师根据校区展示
        $rolesTypes = $request->user()->roles->filter(function ($item) {
            return $item->status == 1;
        })->pluck('type')->unique()->toArray();
        //教务角色没有teacherId
        $teacherId = null;
        if(in_array(SystemRoleTypeEnum::TEACHER,$rolesTypes)){
            $teacherId = $request->user()->teacher->id;
        }
        $result = $this->assessmentBasicService->getAssessmentList($schoolId, $type, 'teacher', $user_id, $teacherId);
        
        return $this->success($result);
    }

    /**
     * 综合报告列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function compositeReportList(Request $request)
    {
        $query = $this->assessmentTeacherService->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('students.id', 'desc')->get();
        return $this->paginateSuccess($list, $cnt);
    }
}
