<?php

namespace App\Models\School\Xuezhi;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OccupationType extends Model
{
    protected $table = 'occupation_type';
    
    protected $fillable = [
        'occupation_name'
    ];

    public $timestamps = false;

    /**
     * 关联职业视频
     */
    public function occupationVideos(): HasMany
    {
        return $this->hasMany(OccupationVideo::class, 'occupation_type_id', 'id');
    }

    /**
     * 获取未删除的职业视频
     */
    public function activeOccupationVideos(): Has<PERSON><PERSON>
    {
        return $this->hasMany(OccupationVideo::class, 'occupation_type_id', 'id')
                    ->where('is_delete', 0);
    }
}
