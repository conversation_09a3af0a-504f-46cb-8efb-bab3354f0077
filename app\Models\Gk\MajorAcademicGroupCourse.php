<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MajorAcademicGroupCourse extends BaseModel
{
    use HasFactory;

    protected $table = 'MajorAcademicGroupCourse';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    // 主键
    protected $primaryKey = 'ID';

    // 是否使用时间戳
    public $timestamps = false;

    // 允许批量赋值字段（根据表结构选择性添加）
    protected $fillable = [
        'ProvinceId', 'Phase', 'SubjectID', 'CategoryID', 'MajorID', 'CourseName',
        'AccessControl', 'CollegeId', 'Tag', 'PlayListID', 'CourseContent', 'CoursePhoto',
        'PlayTotal', 'State', 'Click', 'UPClick', 'AcademicGroupId', 'Adder', 'AddTime', 'Editer', 'EditTime'
    ];
}

