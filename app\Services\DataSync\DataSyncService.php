<?php

namespace App\Services\DataSync;

use App\Services\BaseService;
use App\Services\DataSync\TeacherClassSyncService;
use Illuminate\Support\Facades\Log;

/**
 * 数据同步服务统一入口
 *
 * 该服务作为各个专门同步服务的统一入口，提供向后兼容的接口
 */
class DataSyncService extends BaseService
{
    protected $schoolSyncService;
    protected $classSyncService;
    protected $teacherSyncService;
    protected $studentSyncService;
    protected $teacherClassSyncService;

    // 同步开关配置
    protected $syncEnabled = true; // 总开关

    public function __construct(
        SchoolSyncService $schoolSyncService,
        ClassSyncService $classSyncService,
        TeacherSyncService $teacherSyncService,
        StudentSyncService $studentSyncService,
        TeacherClassSyncService $teacherClassSyncService
    ) {
        $this->schoolSyncService = $schoolSyncService;
        $this->classSyncService = $classSyncService;
        $this->teacherSyncService = $teacherSyncService;
        $this->studentSyncService = $studentSyncService;
        $this->teacherClassSyncService = $teacherClassSyncService;

        // 从配置文件中读取同步开关设置
        $this->initSyncConfig();
    }

    /**
     * 初始化同步配置
     */
    private function initSyncConfig(): void
    {
        $this->syncEnabled = config('sync.enabled', true);
    }

    /**
     * 检查同步是否启用
     *
     * @return bool
     */
    public function isSyncEnabled(): bool
    {
        return $this->syncEnabled;
    }



    // ==================== 学校同步相关方法 ====================

    /**
     * 同步学校数据
     *
     * @param array $schoolData 学校数据
     * @param \Illuminate\Http\Request|null $request 请求对象（可选）
     * @return array
     */
    public function syncSchool(array $schoolData, $request = null): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        // 如果传递了request，在内部处理额外数据
        if ($request) {
            $additionalData = [
                'add_time' => $request->input('add_time', now()->format('Y-m-d H:i:s')),
                'date_due' => $request->input('date_due'),
                'buy_modules' => $request->input('buy_modules', ''),
                'location' => $request->input('location', ''),
                'province' => $request->input('province', ''),
                'city' => $request->input('city', ''),
                'district' => $request->input('district', ''),
                'address' => $request->input('address', ''),
            ];

            // 合并学校数据和额外数据
            $schoolData = array_merge($schoolData, $additionalData);
        

        }

        return $this->schoolSyncService->syncSchool($schoolData);
    }

  

    /**
     * 获取同步数据库中的学校ID
     * 
     * @param int $originalSchoolId 原始学校ID
     * @return int|null
     */
    public function getSyncSchoolId(int $originalSchoolId): ?int
    {
        return $this->schoolSyncService->getSyncSchoolId($originalSchoolId);
    }

    /**
     * 获取校区信息
     * 
     * @param int $campusId 校区ID
     * @return array
     */
    public function getCampusInfo(int $campusId): array
    {
        return $this->schoolSyncService->getCampusInfo($campusId);
    }

    // ==================== 班级同步相关方法 ====================

    /**
     * 同步班级数据
     *
     * @param mixed $class 班级数据或班级模型
     * @param \Illuminate\Http\Request|null $request 请求对象
     * @return array
     */
    public function syncClass($class, $request = null): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        // 在服务内部处理数据转换
        $classData = is_array($class) ? $class : $class->toArray();

        return $this->classSyncService->syncClass($classData);
    }

    /**
     * 批量同步班级数据
     *
     * @param array $classes 班级数据数组或班级模型数组
     * @param \Illuminate\Http\Request|null $request 请求对象（可选）
     * @return array
     */
    public function syncBatchClasses($classes, $request = null): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        // 在内部处理班级数据转换
        $classesData = collect($classes)->map(function ($class) {
            return is_array($class) ? $class : $class->toArray();
        })->toArray();

        return $this->classSyncService->syncBatchClasses($classesData);
    }

    // ==================== 教师同步相关方法 ====================

    /**
     * 同步单个教师数据
     * 
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function syncSingleTeacher($request): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->teacherSyncService->syncSingleTeacher($request);
    }

    /**
     * 同步教师数据到ysy_member和ysy_teacher表
     * 
     * @param array $teacherData 教师数据
     * @return array
     */
    public function syncTeacher(array $teacherData): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->teacherSyncService->syncTeacher($teacherData);
    }

    /**
     * 批量同步教师更新
     *
     * @param array $teachers
     * @return array
     */
    public function syncBatchTeachersUpdate(array $teachers): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->teacherSyncService->syncBatchTeachersUpdate($teachers);
    }

    /**
     * 批量同步教师数据
     *
     * @param array $request_data 包含school_campus_id和teachers数组的请求数据
     * @return array
     */
    public function syncBatchTeachers(array $request_data): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->teacherSyncService->syncBatchTeachers($request_data);
    }

    /**
     * 同步单个教师更新
     *
     * @param \App\Models\School\System\Teacher $teacher
     * @return array
     */
    public function syncSingleTeacherUpdate(\App\Models\School\System\Teacher $teacher): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->teacherSyncService->syncSingleTeacherUpdate($teacher);
    }



    // ==================== 学生同步相关方法 ====================

    /**
     * 同步单个学生数据
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function syncSingleStudent($request): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->studentSyncService->syncSingleStudent($request);
    }

    /**
     * 同步学生数据到ysy_member和ysy_student表
     * 
     * @param array $studentData 学生数据
     * @return array
     */
    public function syncStudent(array $studentData): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->studentSyncService->syncStudent($studentData);
    }

    /**
     * 批量同步学生数据
     *
     * @param \Illuminate\Http\Request $request 请求对象
     * @return array
     */
    public function syncBatchStudents($request): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->studentSyncService->syncBatchStudents($request);
    }

    // ==================== 教师带班同步方法 ====================

    /**
     * 同步教师带班信息
     *
     * @param array $data 带班数据
     * @return array
     */
    public function syncTeacherClasses(array $data): array
    {
        // 检查同步是否启用
        if (!$this->isSyncEnabled()) {
            return [
                'success' => true,
                'message' => '数据同步已关闭',
                'skipped' => true
            ];
        }

        return $this->teacherClassSyncService->syncTeacherClasses($data);
    }

    // ==================== 通用方法 ====================

    /**
     * 获取所有同步服务的状态
     *
     * @return array
     */
    public function getServicesStatus(): array
    {
        return [
            'school_sync_service' => class_exists(SchoolSyncService::class),
            'class_sync_service' => class_exists(ClassSyncService::class),
            'teacher_sync_service' => class_exists(TeacherSyncService::class),
            'student_sync_service' => class_exists(StudentSyncService::class),
            'message' => '所有同步服务已拆分并可用'
        ];
    }

    /**
     * 记录同步操作日志
     * 
     * @param string $operation 操作类型
     * @param array $data 数据
     * @param bool $success 是否成功
     * @return void
     */
    public function logSyncOperation(string $operation, array $data, bool $success): void
    {
        $logLevel = $success ? 'info' : 'error';
        $message = $success ? "{$operation}同步成功" : "{$operation}同步失败";
        
        Log::$logLevel($message, $data);
    }
    
}
