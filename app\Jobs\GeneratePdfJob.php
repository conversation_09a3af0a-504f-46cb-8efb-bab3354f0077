<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\School\Assessment\PdfGeneratorService;
use Throwable;

class GeneratePdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    /**
     * Create a new job instance.
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(PdfGeneratorService $pdfGeneratorService)
    {
        try {
            Log::info('开始生成pdf', ['params' => $this->params]);

            $assessment_id = $this->params['assessment_id'];
            $assessment_task_assignment_id = $this->params['assessment_task_assignment_id'];

            # 生成pdf并自动更新状态
            $pdf_url = $pdfGeneratorService->generatePdf(
                $assessment_id,
                $assessment_task_assignment_id
            );

            Log::info('pdf已生成', [
                'pdf_url' => $pdf_url
            ]);
        } catch (\Exception $e) {
            throw new \Exception("pdf生成失败", 500, $e);
        }
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        // 向用户发送失败通知等...
    }
}
