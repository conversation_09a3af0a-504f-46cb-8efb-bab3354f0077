<?php

namespace App\Exceptions;

use App\Traits\ApiResponse;
use App\Traits\DingDingMessage;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class BusinessException extends ExceptionHandler
{
    use ApiResponse;
    use DingDingMessage;

    protected $code;
    protected $message;
    protected $log_data;
    protected $log_type;

    public function __construct($message = '业务异常', $code = 500, $log_data = null, $log_type = 'error')
    {
        $this->message = $message;
        $this->code = $code;
        $this->log_data = $log_data;
        $this->log_type = $log_type;
        
        parent::__construct($this->message, $this->code);
    }

    public function render($request, Throwable $e)
    {
        // 记录日志
        $log_data = $this->log_data;
        $log_type = $this->log_type;
        // 如果传入了log_data，则记录日志
        if (!is_null($log_data)) {
            switch ($log_type) {
                case 'info':
                    Log::info($this->message, ['data' => $log_data]);
                    break;
                case 'warning':
                    Log::warning($this->message, ['data' => $log_data]);
                    break;
                case 'debug':
                    Log::debug($this->message, ['data' => $log_data]);
                    break;
                default:
                    Log::error($this->message, ['data' => $log_data]);
                    break;
            }
        }

        $str = '- 操作人: ' . ($request->user() ? $request->user()->username : '未登录') . "\n"
            . '- 操作时间: ' . date('Y-m-d H:i:s') . "\n"
            . '- 访问了: ' . $request->route()->getName() . "\n"
//            . '- ip地址: ' . $request->ip() . "\n"
//            . '- 服务器ip: '. gethostbyname(gethostname()). "\n"
//            . '- 请求方式: ' . $request->getMethod() . "\n"
            . '- 错误接口: ' . urldecode($request->fullUrl()) . "\n"
            . '- 错误信息: ' . substr($e->getMessage(), 0, 200) . "\n";
        $this->send_dingding_message($str, '业务异常');

        return $this->error($this->message, $this->code ?? 500);
    }
}