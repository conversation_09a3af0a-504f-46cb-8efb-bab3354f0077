# 职业视频接口使用示例

## 接口地址前缀

所有接口都需要在前面加上基础路径：`/api/school/xuezhi/`

## 使用示例

### 1. 获取职业视频列表

```bash
# 获取所有职业视频（分页）
GET /api/school/xuezhi/occupation-video/list

# 根据职业类型筛选
GET /api/school/xuezhi/occupation-video/list?occupation_type_id=1

# 根据地区筛选
GET /api/school/xuezhi/occupation-video/list?xzq_id=110000

# 根据标题搜索
GET /api/school/xuezhi/occupation-video/list?title=软件工程师

# 组合筛选条件和分页
GET /api/school/xuezhi/occupation-video/list?occupation_type_id=1&xzq_id=110000&title=软件&per_page=20&page=1

# 获取第2页，每页50条记录
GET /api/school/xuezhi/occupation-video/list?per_page=50&page=2

# 最大分页限制示例
GET /api/school/xuezhi/occupation-video/list?per_page=100&page=100
```

### 2. 获取职业视频详情

```bash
# 获取ID为1的职业视频详情
GET /api/school/xuezhi/occupation-video/1
```

### 3. 获取职业类型列表

```bash
# 获取所有职业类型
GET /api/school/xuezhi/occupation-type/list

# 搜索职业类型
GET /api/school/xuezhi/occupation-type/list?occupation_name=计算机

# 只获取有视频的职业类型
GET /api/school/xuezhi/occupation-type/list?has_videos=true
```

### 4. 获取职业类型详情

```bash
# 获取ID为1的职业类型详情
GET /api/school/xuezhi/occupation-type/1
```

### 5. 获取职业类型及视频数量统计

```bash
# 获取所有职业类型及其对应的视频数量
GET /api/school/xuezhi/occupation-type/with-count
```

## JavaScript 调用示例

```javascript
// 使用 axios 获取职业视频列表
async function getOccupationVideos(params = {}) {
    try {
        const response = await axios.get('/api/school/xuezhi/occupation-video/list', {
            params: params
        });
        return response.data;
    } catch (error) {
        console.error('获取职业视频列表失败:', error);
        throw error;
    }
}

// 使用示例
getOccupationVideos({
    occupation_type_id: 1,
    xzq_id: 110000,
    title: '软件',
    per_page: 20,
    page: 1
}).then(data => {
    console.log('职业视频列表:', data);
    console.log('分页信息:', data.data.pagination);
    console.log('当前页:', data.data.pagination.current_page);
    console.log('总页数:', data.data.pagination.last_page);
    console.log('总记录数:', data.data.pagination.total);
    console.log('是否有更多页:', data.data.pagination.has_more_pages);
});

// 获取职业类型列表
async function getOccupationTypes() {
    try {
        const response = await axios.get('/api/school/xuezhi/occupation-type/list');
        return response.data;
    } catch (error) {
        console.error('获取职业类型列表失败:', error);
        throw error;
    }
}

// 分页处理示例
class OccupationVideoList {
    constructor() {
        this.currentPage = 1;
        this.perPage = 15;
        this.totalPages = 1;
        this.videos = [];
    }

    // 加载指定页的数据
    async loadPage(page = 1, filters = {}) {
        try {
            const params = {
                page: page,
                per_page: this.perPage,
                ...filters
            };

            const response = await getOccupationVideos(params);

            this.videos = response.data.data;
            this.currentPage = response.data.pagination.current_page;
            this.totalPages = response.data.pagination.last_page;

            return response.data;
        } catch (error) {
            console.error('加载页面数据失败:', error);
            throw error;
        }
    }

    // 下一页
    async nextPage(filters = {}) {
        if (this.currentPage < this.totalPages) {
            return await this.loadPage(this.currentPage + 1, filters);
        }
        return null;
    }

    // 上一页
    async prevPage(filters = {}) {
        if (this.currentPage > 1) {
            return await this.loadPage(this.currentPage - 1, filters);
        }
        return null;
    }

    // 跳转到指定页
    async goToPage(page, filters = {}) {
        if (page >= 1 && page <= this.totalPages) {
            return await this.loadPage(page, filters);
        }
        return null;
    }
}

// 使用分页类的示例
const videoList = new OccupationVideoList();

// 加载第一页
videoList.loadPage(1, { occupation_type_id: 1 }).then(data => {
    console.log('第一页数据:', data);
});

// 加载下一页
videoList.nextPage({ occupation_type_id: 1 }).then(data => {
    if (data) {
        console.log('下一页数据:', data);
    } else {
        console.log('已经是最后一页');
    }
});
```

## PHP 调用示例

```php
// 在控制器中使用服务
use App\Services\School\Xuezhi\OccupationVideoService;

class ExampleController extends Controller
{
    protected OccupationVideoService $occupationVideoService;

    public function __construct(OccupationVideoService $occupationVideoService)
    {
        $this->occupationVideoService = $occupationVideoService;
    }

    public function getVideos(Request $request)
    {
        // 获取职业视频列表
        $videos = $this->occupationVideoService->getOccupationVideos($request);
        
        // 获取职业类型列表
        $types = $this->occupationVideoService->getOccupationTypes($request);
        
        return view('videos', compact('videos', 'types'));
    }
}
```

## 数据库表结构说明

### occupation_vedio 表
- `id`: 主键
- `title`: 视频标题
- `vid`: 视频ID
- `xzq_id`: 地区ID
- `cover`: 封面图片
- `is_delete`: 是否删除（0：未删除，1：已删除）
- `occupation_type_id`: 职业类型ID
- `video_id`: 视频ID

### occupation_type 表
- `id`: 主键
- `occupation_name`: 职业名称

## 注意事项

1. 所有接口都会自动过滤已删除的记录（is_delete = 0）
2. 分页参数 `per_page` 最大值为 100
3. 职业视频详情接口会关联查询职业类型信息
4. 支持多种筛选条件的组合使用
