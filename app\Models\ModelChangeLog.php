<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class ModelChangeLog extends BaseModel
{
    use HasFactory;
    protected $fillable = [
        'model_type',
        'model_id',
        'action',
        'before',
        'after',
        'changed',
        'user_id',
        'user_name',
        'ip_address',
        'request_url',
    ];

    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'before' => 'array',
        'after' => 'array',
        'changed' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
