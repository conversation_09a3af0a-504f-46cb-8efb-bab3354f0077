<?php

namespace App\Http\Requests\School\Xuezhi;

use App\Http\Requests\BaseRequest;

class CollegeRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'index' => $this->indexRules(),
            'rank' => $this->rankRules(),
            default => []
        };
    }

    private function indexRules(): array
    {
        return [
            'province_id' => 'nullable|array',
            'province_id.*' => 'integer',
            'college_name' => 'nullable|string',
            'college_tags' => 'nullable|array',
            'college_tags.*' => 'string',
            'yxtype' => 'nullable|array',
            'yxtype.*' => 'string',
            'yxjbz' => 'nullable|array',
            'yxjbz.*' => 'integer',
            'levels' => 'nullable|array',
            'levels.*' => 'string',
        ];
    }

    private function rankRules(): array
    {
        return [
            'type' => 'required|integer|between:1,7',
            'rank_type' => 'nullable|string',
            'num' => 'nullable|integer|min:1'
        ];
    }

    public function messages(): array
    {
        return [
            'province_id.array' => '省份ID必须是数组',
            'province_id.*.integer' => '省份ID必须是整数',
            'college_name.string' => '院校名称必须是字符串',
            'college_tags.array' => '院校标签必须是数组',
            'college_tags.*.string' => '院校标签必须是字符串',
            'yxtype.array' => '院校类型必须是数组',
            'yxtype.*.string' => '院校类型必须是字符串',
            'yxjbz.array' => '院校级别必须是数组',
            'yxjbz.*.integer' => '院校级别必须是整数',
            'levels.array' => '层次必须是数组',
            'levels.*.string' => '层次必须是字符串',
            'type.required' => '类型不能为空',
            'type.integer' => '类型必须为整数',
            'type.between' => '类型值只能为1到7之间',
            'rank_type.string' => '排名类型必须是字符串',
            'num.integer' => '评估轮次必须是整数',
            'num.min' => '评估轮次至少为1',
        ];
    }
}