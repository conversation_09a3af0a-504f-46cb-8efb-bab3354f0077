<?php

// 组装缓存键
function keyBuilder($tagKey, ... $params)
{
    if (empty($tagKey))
        return '';

    if(!empty($params)){
        $params = array_map(function ($param) {
            return is_array($param) ? implode(':', $param) : $param;
        }, $params);
        return $tagKey . ':' . implode(':', $params);
    } else {
        return $tagKey;
    }
}


function searchBetweenDate($dates): array
{
    if (!$dates)
        return [];
    return [\Carbon\Carbon::parse($dates[0])->startOfDay(), \Carbon\Carbon::parse($dates[1])->endOfDay()];
}

function filterRequestData($tableName, $connection = 'mysql'): array
{
    $fieldArray = \Illuminate\Support\Facades\Schema::connection($connection)->getColumnListing($tableName);
    $fieldArray = array_filter($fieldArray, function ($item) {
        return $item !== 'id';
    });
    $reqArray = request()->all();
    // 取数据库和请求数据的交集
    return array_intersect_key($reqArray, array_flip($fieldArray));
}

// 判断jwt token是否过期：true 过期，false 未过期
function jwtTokenExpired()
{
    // 从请求头里获取token
    $token = request()->header('Authorization');
    // 判断请求头里是否有token
    if (!$token) return true;

    try {
        // 验证token是否过期
        \Tymon\JWTAuth\Facades\JWTAuth::parseToken()->authenticate();
        return false;
    } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
        return true;
    }
}


/**
 * 获取当前学年
 *
 * @return int 学年（年份）
 */
function getCurrentSchoolYear(): int
{
    $now = now();
    $year = $now->year;
    $month = $now->month;

    // 如果当前日期在7月份或之前，则属于上一学年
    if ($month <= 8) {
        return $year - 1;
    }

    // 否则属于当前学年（9月1日开始的新学年）
    return $year;
}

/**
 * 根据测评ID获取年级类型
 *
 * @param int $assessmentId 测评ID
 * @return int|null 年级类型：1=初中，2=高中，null=不限
 */
function getGradeType(int $assessmentId): ?int
{
    // 根据测评ID判断是初中还是高中
    // 2、4、5是高中版本
    // 6、7、8是初中版本
    if (in_array($assessmentId, [2, 4, 5])) {
        return 2; // 高中
    } elseif (in_array($assessmentId, [6, 7, 8])) {
        return 1; // 初中
    }

    // 默认不限年级类型
    return null;
}