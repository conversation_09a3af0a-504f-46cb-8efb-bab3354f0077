<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('model_change_logs', function (Blueprint $table) {
        $table->id();
        $table->string('model_type');           // 模型类名
        $table->unsignedBigInteger('model_id')->nullable(); // 模型ID（create 时可为 null）
        $table->string('action');               // create / update / delete
        $table->json('before')->nullable();     // 修改前数据
        $table->json('after')->nullable();      // 修改后数据
        $table->json('changed')->nullable();    // 改变了哪些字段
        $table->unsignedBigInteger('user_id')->nullable();
        $table->string('user_name')->nullable();
        $table->string('ip_address')->nullable();
        $table->string('request_url')->nullable();
        $table->timestamps();
    });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('model_change_logs');
    }
};
