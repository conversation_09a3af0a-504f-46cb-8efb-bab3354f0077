<?php

namespace App\Models\School\Xuezhi;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OccupationVideo extends Model
{
    protected $table = 'occupation_vedio';
    
    protected $fillable = [
        'title',
        'vid',
        'xzq_id',
        'cover',
        'is_delete',
        'occupation_type_id',
        'video_id'
    ];

    protected $casts = [
        'is_delete' => 'boolean',
        'occupation_type_id' => 'integer',
        'xzq_id' => 'integer',
        'video_id' => 'string'
    ];

    public $timestamps = false;

    /**
     * 关联职业类型
     */
    public function occupationType(): BelongsTo
    {
        return $this->belongsTo(OccupationType::class, 'occupation_type_id', 'id');
    }

    /**
     * 作用域：未删除的记录
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('is_delete', 0);
    }

    /**
     * 作用域：根据职业类型筛选
     */
    public function scopeByOccupationType($query, $occupationTypeId)
    {
        if ($occupationTypeId) {
            return $query->where('occupation_type_id', $occupationTypeId);
        }
        return $query;
    }

    /**
     * 作用域：根据地区筛选
     */
    public function scopeByXzq($query, $xzqId)
    {
        if ($xzqId) {
            return $query->where('xzq_id', $xzqId);
        }
        return $query;
    }
}
