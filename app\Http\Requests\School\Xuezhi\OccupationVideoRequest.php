<?php

namespace App\Http\Requests\School\Xuezhi;

use App\Http\Requests\BaseRequest;

class OccupationVideoRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route() ? $this->route()->getActionMethod() : 'index';

        switch ($method) {
            case 'index':
                return $this->indexRules();
            case 'show':
                return $this->showRules();
            case 'occupationTypes':
                return $this->occupationTypesRules();
            default:
                return [];
        }
    }

    /**
     * 合并路由参数到验证数据，确保能校验 path 参数（例如 /{id}）
     */
    public function validationData(): array
    {
        return array_merge($this->all(), $this->route()?->parameters() ?? []);
    }

    /**
     * 职业视频列表验证规则
     */
    private function indexRules(): array
    {
        return [
            'occupation_type_id' => 'nullable|integer|exists:occupation_type,id',
            'xzq_id' => 'nullable|integer|min:1',
            'title' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1|max:10000' // 限制最大页码防止恶意请求
        ];
    }

    /**
     * 职业视频详情验证规则
     */
    private function showRules(): array
    {
        return [
            'id' => 'required|integer|exists:occupation_vedio,id'
        ];
    }

    /**
     * 职业类型列表验证规则
     */
    private function occupationTypesRules(): array
    {
        return [
            'occupation_name' => 'nullable|string|max:255',
            'has_videos' => 'nullable|boolean'
        ];
    }

    /**
     * 自定义验证消息
     */
    public function messages(): array
    {
        return [
            'occupation_type_id.exists' => '职业类型不存在',
            'xzq_id.integer' => '地区ID必须是整数',
            'xzq_id.min' => '地区ID必须大于0',
            'title.string' => '标题必须是字符串',
            'title.max' => '标题长度不能超过255个字符',
            'per_page.integer' => '每页数量必须是整数',
            'per_page.min' => '每页数量最少为1',
            'per_page.max' => '每页数量最多为100',
            'page.integer' => '页码必须是整数',
            'page.min' => '页码最少为1',
            'page.max' => '页码不能超过10000',
            'id.required' => 'ID是必需的',
            'id.integer' => 'ID必须是整数',
            'id.exists' => '职业视频不存在',
            'occupation_name.string' => '职业名称必须是字符串',
            'occupation_name.max' => '职业名称长度不能超过255个字符',
            'has_videos.boolean' => 'has_videos必须是布尔值'
        ];
    }
}
