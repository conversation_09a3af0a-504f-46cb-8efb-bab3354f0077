<?php

namespace App\Models\Admin;

use App\Models\BaseModel;
use App\Models\Role;
use App\Traits\ModelChangeLogTrait;

class OrganizationHasMenu extends BaseModel
{
    use ModelChangeLogTrait;

    protected $guarded = [];

    // 登录用户根据角色获取菜单（多对多关系，表示当前模型（组织菜单）与 Role 模型通过 role_has_menus 表关联，关联键分别为 organization_menu_id 和 role_id。）
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_has_menus', 'organization_menu_id', 'role_id');
    }

    // 对应菜单表
    public function menu()
    {
        return $this->belongsTo(Menu::class, 'menu_id', 'id')->select('id', 'menu_name', 'url', 'icon');
    }

    // 获取子菜单
    public function childrenCategories()
    {
        return $this->hasMany(self::class, 'parent_id', 'menu_id')
            ->where('status', 1)
            ->select('id', 'menu_id', 'parent_id', 'organization_id', 'menu_alias')
            ->with('menu')
            ->orderBy('sort');
    }

    // 递归获取子菜单
    public function children()
    {
        return $this->childrenCategories()->with('children');
    }
}
