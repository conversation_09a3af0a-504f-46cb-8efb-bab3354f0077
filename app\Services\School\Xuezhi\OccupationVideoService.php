<?php

namespace App\Services\School\Xuezhi;

use App\Models\School\Xuezhi\OccupationVideo;
use App\Models\School\Xuezhi\OccupationType;
use App\Services\BaseService;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class OccupationVideoService extends BaseService
{
    /**
     * 获取职业视频列表
     *
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function getOccupationVideos(Request $request): LengthAwarePaginator
    {
        $query = OccupationVideo::query()
            ->with(['occupationType:id,occupation_name'])
            ->notDeleted();

        // 根据职业类型筛选
        if ($request->filled('occupation_type_id')) {
            $query->byOccupationType($request->occupation_type_id);
        }

        // 根据地区筛选
        if ($request->filled('xzq_id')) {
            $query->byXzq($request->xzq_id);
        }

        // 搜索标题
        if ($request->filled('title')) {
            $query->where('title', 'like', '%' . $request->title . '%');
        }

        // 排序
        $query->orderBy('id', 'desc');

        // 分页参数处理：前端传多少就多少（默认15）；不做上限限制
        $perPage = (int) $request->get('per_page', 15);
        $perPage = $perPage > 0 ? $perPage : 15;

        $page = (int) $request->get('page', 1);
        $page = $page > 0 ? $page : 1;

        // 分页查询
        $videos = $query->paginate($perPage, ['*'], 'page', $page);

        // 列表中追加视频签名信息
        $videos->getCollection()->transform(function ($item) {
            if (!empty($item->video_id)) {
                $item->video = $this->getVideoEncrypt((string) $item->video_id);
            }
            return $item;
        });

        return $videos;
    }

    /**
     * 获取职业视频详情
     *
     * @param int $id
     * @return OccupationVideo|null
     */
    public function getOccupationVideoDetail(int $id): ?OccupationVideo
    {
        $video = OccupationVideo::with(['occupationType:id,occupation_name'])
            ->notDeleted()
            ->find($id);

        if (!$video) {
            return null;
        }
       
        // 追加视频签名信息
        if (!empty($video->video_id)) {
            $video->video = $this->getVideoEncrypt((string) $video->video_id);
        }

        return $video;
    }

    /**
     * 生成视频签名
     * @param string $video_id
     * @return array{sign:string,ts:int}
     */
    private function getVideoEncrypt(string $video_id): array
    {
        $secretkey = 'hcTDd2tF9j';
        $ts = now()->timestamp;
        $sign = md5($secretkey . $video_id . $ts);

        return [
            'sign' => $sign,
            'ts' => $ts,
        ];
    }

    /**
     * 获取职业类型列表
     *
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOccupationTypes(Request $request)
    {
        $query = OccupationType::query();

        // 搜索职业名称
        if ($request->filled('occupation_name')) {
            $query->where('occupation_name', 'like', '%' . $request->occupation_name . '%');
        }

        // 是否只返回有视频的职业类型
        if ($request->boolean('has_videos')) {
            $query->whereHas('activeOccupationVideos');
        }

        return $query->select('id', 'occupation_name')
            ->orderBy('id', 'asc')
            ->get();
    }

    /**
     * 根据职业类型ID获取职业类型信息
     *
     * @param int $id
     * @return OccupationType|null
     */
    public function getOccupationTypeById(int $id): ?OccupationType
    {
        return OccupationType::select('id', 'occupation_name')->find($id);
    }

    /**
     * 获取职业类型的视频统计
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOccupationTypeWithVideoCount()
    {
        return OccupationType::withCount(['activeOccupationVideos as video_count'])
            ->select('id', 'occupation_name')
            ->orderBy('id', 'asc')
            ->get();
    }
}
