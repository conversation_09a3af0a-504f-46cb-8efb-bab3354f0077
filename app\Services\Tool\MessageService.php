<?php

namespace App\Services\Tool;

use App\Notifications\Message;
use App\Services\BaseService;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class MessageService extends BaseService
{
    /**
     * 发送消息通知
     * 
     * @param int|array $receiverIds 接收者ID数组
     * @param array $notification 通知数据
     * @return bool
     */
    public function sendMessage($receiverIds, array $notification): bool
    {
        try {
            // 如果是单个ID，转换为数组
            if (!is_array($receiverIds)) {
                $receiverIds = [$receiverIds];
            }

            // 创建消息通知实例
            $message = new Message($notification);
            // 发送消息给所有接收者
            foreach ($receiverIds as $receiverId) {
                $user = User::find($receiverId);
                if ($user) {
                    $user->notify($message);
                }
            }
            
            return true;
        } catch (\Exception $e) {
            $this->throwBusinessException('发送消息失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取未读消息数量
     */
    public function getUnreadMessageNum()
    {
        try {
            // 获取当前用户
            $user = Auth::user();
            if (!$user) {
                $this->throwBusinessException('用户未登录', 401);
            }

            return $user->unreadNotifications()->count();
        } catch (\Exception $e) {
            $this->throwBusinessException('获取消息列表失败', 500, $e->getMessage(), 'error');
        }
    }

    /**
     * 获取消息列表
     *
     * @param \Illuminate\Http\Request $request
     */
    public function getMessageList($request)
    {
        try {
            // 获取当前用户
            $user = Auth::user();
            if (!$user) {
                $this->throwBusinessException('用户未登录', 401);
            }

            // 获取查询参数
            $isRead = $request->input('is_read'); // null: 全部, 0: 未读, 1: 已读
            $type = $request->input('type'); // 消息类型

            // 构建查询
            $query = $user->notifications();

            // 按已读/未读筛选
            if ($isRead === '0') {
                $query = $user->unreadNotifications();
            } elseif ($isRead === '1') {
                $query = $user->readNotifications();
            }

            // 按类型筛选
            if ($type) {
                $query->where('data->type', $type);
            }

            $list = $query->orderBy('created_at', 'desc')->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->data['title'] ?? '',
                        'content' => $notification->data['content'] ?? '',
                        'type' => $notification->data['type'] ?? '',
                        'data' => $notification->data['data'] ?? [],
                        'url' => $notification->data['url'] ?? '',
                        'url_type' => $notification->data['url_type'] ?? '',
                        'is_read' => !is_null($notification->read_at),
                        'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
                        'read_at' => $notification->read_at ? $notification->read_at->format('Y-m-d H:i:s') : null,
                    ];
                });

            return $list;
        } catch (\Exception $e) {
            $this->throwBusinessException('获取消息列表失败', 500, $e->getMessage(), 'error');
        }
    }

    /**
     * 获取消息详情
     * 
     * @param string $id 消息ID
     * @return array
     */
    public function getMessageDetail(string $id): array
    {
        try {
            // 获取当前用户
            $user = Auth::user();
            if (!$user) {
                $this->throwBusinessException('用户未登录', 401);
            }
            
            // 查找消息
            $notification = $user->notifications()->where('id', $id)->first();
            if (!$notification) {
                $this->throwBusinessException('消息不存在', 404);
            }
            
            // 标记为已读
            if (is_null($notification->read_at)) {
                $notification->markAsRead();
            }
            
            // 格式化结果
            return [
                'id' => $notification->id,
                'title' => $notification->data['title'] ?? '',
                'content' => $notification->data['content'] ?? '',
                'type' => $notification->data['type'] ?? '',
                'data' => $notification->data['data'] ?? [],
                'url' => $notification->data['url'] ?? '',
                'url_type' => $notification->data['url_type'] ?? '',
                'is_read' => true,
                'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
                'read_at' => $notification->read_at->format('Y-m-d H:i:s'),
            ];
        } catch (\Exception $e) {
            $this->throwBusinessException('获取消息详情失败', 500, $e->getMessage(), 'error');
        }
    }
    
    /**
     * 标记消息为已读
     * 
     * @param string|array $ids 消息ID或ID数组
     * @return bool
     */
    public function markAsRead($ids): bool
    {
        try {
            // 获取当前用户
            $user = Auth::user();
            if (!$user) {
                $this->throwBusinessException('用户未登录', 401);
            }
            
            // 如果是单个ID，转换为数组
            if (!is_array($ids)) {
                $ids = [$ids];
            }
            
            // 标记消息为已读
            foreach ($ids as $id) {
                $notification = $user->notifications()->where('id', $id)->first();
                if ($notification && is_null($notification->read_at)) {
                    $notification->markAsRead();
                }
            }
            
            return true;
        } catch (\Exception $e) {
            $this->throwBusinessException('标记消息已读失败', 500, $e->getMessage(), 'error');
        }
    }
    
    /**
     * 标记所有消息为已读
     * 
     * @return bool
     */
    public function markAllAsRead(): bool
    {
        try {
            // 获取当前用户
            $user = Auth::user();
            if (!$user) {
                $this->throwBusinessException('用户未登录', 401);
            }
            
            // 标记所有未读消息为已读
            $user->unreadNotifications->markAsRead();
            
            return true;
        } catch (\Exception $e) {
            $this->throwBusinessException('标记所有消息已读失败', 500, $e->getMessage(), 'error');
        }
    }
    
    /**
     * 删除消息
     * 
     * @param string|array $ids 消息ID或ID数组
     * @return bool
     */
    public function deleteMessage($ids): bool
    {
        try {
            // 获取当前用户
            $user = Auth::user();
            if (!$user) {
                $this->throwBusinessException('用户未登录', 401);
            }
            
            // 如果是单个ID，转换为数组
            if (!is_array($ids)) {
                $ids = [$ids];
            }
            
            // 删除消息
            foreach ($ids as $id) {
                $notification = $user->notifications()->where('id', $id)->first();
                if ($notification) {
                    $notification->delete();
                }
            }
            
            return true;
        } catch (\Exception $e) {
            $this->throwBusinessException('删除消息失败', 500, $e->getMessage(), 'error');
        }
    }
    
    
}