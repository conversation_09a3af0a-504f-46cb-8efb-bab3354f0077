<?php

namespace App\Services\DataSync;

use App\Models\User;
use App\Models\School\System\SchoolCampus;
use App\Services\BaseService;
use App\Services\School\System\ClassService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TeacherSyncService extends BaseService
{
    protected $syncConnection;
    protected $schoolSyncService;
    protected $classService;

    public function __construct(SchoolSyncService $schoolSyncService, ClassService $classService)
    {
        $this->syncConnection = DB::connection('sync_mysql');
        $this->schoolSyncService = $schoolSyncService;
        $this->classService = $classService;
    }

    /**
     * 同步单个教师数据（保持原有逻辑）
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function syncSingleTeacher($request): array
    {
        $name = $request->input('teacher_name');
        $username = $request->input('username');
        $password = '827ccb0eea8a706c4c34a16891f84e7b';
        $gender = $request->input('gender', 1); // 默认为1（男）
        $school_district = $request->input('school_campus_id'); // [2,3]这样的数组
        $roles = $request->input('roles', $request->input('role_type', []));

        Log::info('教师同步参数', [
            'teacher_name' => $name,
            'username' => $username,
            'school_district' => $school_district,
            'roles' => $roles
        ]);

        // 获取或创建校区信息
        $districtInfo = $this->getOrCreateDistrict($school_district);
       
        if (!$districtInfo) {
            Log::error('获取或创建校区信息失败', ['school_campus_id' => $school_district]);
            $this->throwBusinessException('获取或创建校区信息失败');
        }

        $syncDistrictId = $districtInfo['district_id'];
        $syncSchoolId = $districtInfo['school_id'];

        Log::info('校区信息获取成功', [
            'school_campus_id' => $school_district,
            'sync_district_id' => $syncDistrictId,
            'sync_school_id' => $syncSchoolId
        ]);

        // 如果包含2就是教务，如果只有3就是老师
        if (in_array(2, $roles)) {
            // 教务角色
            $role = $this->getRoleInfo('教务', 2, $syncSchoolId);

            if (!$role) {
                $this->throwBusinessException('未找到教务角色信息（name=教务 或 type=2）');
            }

            $role_id = $role->id;
            $role_source_id = '2';
        } elseif (in_array(3, $roles) && !in_array(2, $roles)) {
            // 只有教师角色（没有教务）
            $role = $this->getRoleInfo('老师', 3, $syncSchoolId);

            if (!$role) {
                $this->throwBusinessException('未找到老师角色信息（name=老师 或 type=3）');
            }

            $role_id = $role->id;
            $role_names = '老师';
            $role_source_id = '3';
        }
 
        // 准备教师教务账号数据
        $teacherData = [
            'name' => $name,
            'teacher_name' => $name, // 添加teacher_name字段
            'username' => $username,
            'password' => '827ccb0eea8a706c4c34a16891f84e7b',
            'role_id' => $role_id,
            'school_id' => $syncSchoolId,
            'school_district' => $syncDistrictId, // 使用正确的校区ID
            'role_source_id' => $role_source_id,
            'step' => '0',
            'roles' => $roles,
            'gender' => $gender
        ];

        // 调用syncTeacher方法
        return $this->syncTeacher($teacherData);
    }

    /**
     * 同步教师数据到ysy_member和ysy_teacher表（保持原有逻辑）
     *
     * @param array $teacherData 教师数据
     * @return array
     */
    public function syncTeacher(array $teacherData): array
    {
        try {
            // 检查同步数据库连接 
            $this->syncConnection->beginTransaction();
            // 检查ysy_member表中是否已存在相同用户名的记录
            $existingMember = $this->syncConnection->table('ysy_member')
                ->where('username', $teacherData['username'])
                ->where('school_id', $teacherData['school_id'])
                ->first();

            if ($existingMember) {
                // 如果member已存在，检查是否需要同步到teacher表
                $member_id = $existingMember->id;

                // 检查ysy_teacher表中是否已存在相同的记录
                if (in_array(3, $teacherData['roles'])) {
                    $existingTeacher = $this->syncConnection->table('ysy_teacher')
                        ->where('member_id', $member_id)
                        ->where('school_id', $teacherData['school_id'])
                        ->first();

                    if ($existingTeacher) {
                        $this->syncConnection->rollBack();
                        return [
                            'success' => true,
                            'message' => '教师数据已存在，跳过同步',
                            'skipped' => true
                        ];
                    }
                } else {
                    $this->syncConnection->rollBack();
                    return [
                        'success' => true,
                        'message' => '用户数据已存在，跳过同步',
                        'skipped' => true
                    ];
                }
            } else {
                // 准备同步到ysy_member表的数据
                $member_data = [
                    'name' => $teacherData['teacher_name'] ?? $teacherData['name'],
                    'username' => $teacherData['username'],
                    'password' => $teacherData['password'],
                    'gender' => isset($teacherData['gender']) ? ($teacherData['gender'] == 1 ? 1 : 2) : 1,
                    'school_id' => $teacherData['school_id'],
                    'school_district' => $teacherData['school_district'],
                    'role_id' => '0,' . $teacherData['role_id'] . ',0', // 使用同步数据库中的角色ID
                    'step' => 0,
                    'role_source_id' => $teacherData['role_source_id'],
                    'create_time' => now(),
                ];
               
                Log::info('准备插入ysy_member表', [
                    'member_data' => $member_data
                ]);

                // 同步到ysy_member表并获取生成的ID
                try {
                    $member_id = $this->syncConnection->table('ysy_member')->insertGetId($member_data);
                    Log::info('ysy_member插入成功', [
                        'member_id' => $member_id
                    ]);
                } catch (\Exception $e) {
                    Log::error('ysy_member插入失败', [
                        'error' => $e->getMessage(),
                        'member_data' => $member_data
                    ]);
                    throw $e;
                }
            }
            $synced_to_teacher = false;

            // 如果角色类型包含教师(type=3)，同步到ysy_teacher表
            if (in_array(3, $teacherData['roles'])) {
                // 生成教师ID
                $teacher_data = [
                    'member_id' => $member_id,
                    'name' => $teacherData['name'],
                    'username' => $teacherData['username'],
                    'gender' => isset($teacherData['gender']) ? ($teacherData['gender'] == 1 ? 1 : 2) : 1,
                    'school_id' => $teacherData['school_id'],
                    'school_district' => $teacherData['school_district'],
                    'step' => 0,
                    'type' => '老师',
                    //是否是心理老师
                    'is_psych' => '0',
                ];
               
                Log::info('准备插入ysy_teacher表', [
                    'teacher_data' => $teacher_data
                ]);

                // 同步到ysy_teacher表
                try {
                    $teacher_id = $this->syncConnection->table('ysy_teacher')->insertGetId($teacher_data);
                    Log::info('ysy_teacher插入成功', [
                        'teacher_id' => $teacher_id
                    ]);
                    $synced_to_teacher = true;
                } catch (\Exception $e) {
                    Log::error('ysy_teacher插入失败', [
                        'error' => $e->getMessage(),
                        'teacher_data' => $teacher_data
                    ]);
                    throw $e;
                }
            }

            $this->syncConnection->commit();

            return [
                'success' => true,
                'message' => '教师数据同步成功'
            ];
        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            Log::error('教师数据同步失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'teacher_data' => $teacherData ?? null
            ]);
            $this->throwBusinessException('教师数据同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量同步教师数据（保持原有逻辑，支持role_name和重复检查）
     *
     * @param array $request_data 包含school_campus_id和teachers数组的请求数据
     * @return array
     */
    public function syncBatchTeachers(array $request_data): array
    {
        if (empty($request_data['teachers'])) {
            return [
                'success' => false,
                'message' => '教师数据为空'
            ];
        }

        $school_campus_id = $request_data['school_campus_id'];
        $teachers = $request_data['teachers'];

        // 预处理角色信息，避免重复查询
        $syncSchoolId = $this->schoolSyncService->getSyncDistrictId($school_campus_id);
        $roleInfo = $this->preprocessTeacherRoles($syncSchoolId);
        if (!$roleInfo) {
            return [
                'success' => false,
                'message' => '预处理角色信息失败'
            ];
        }

        Log::info('批量教师同步开始，角色信息已准备', [
            'campus_id' => $school_campus_id,
            'school_id' => $syncSchoolId,
            'teacher_role_id' => $roleInfo['teacher_role_id'] ?? null,
            'academic_role_id' => $roleInfo['academic_role_id'] ?? null,
            'teacher_count' => count($teachers)
        ]);

        $sync_results = [];
        $success_count = 0;
        $failed_count = 0;
        $skipped_count = 0;

        foreach ($teachers as $teacher_info) {
            // 构造单个教师的请求数据
            $single_request_data = [
                'school_campus_id' => $school_campus_id,
                'teacher_name' => $teacher_info['teacher_name'],
                'username' => $teacher_info['username'],
                'password' => '123456', // 默认密码
                'gender' => $this->convertGenderToNumber($teacher_info['gender'] ?? '男'),
                'role_name' => $teacher_info['role_name']
            ];

            // 转换role_name为roles数组
            if ($teacher_info['role_name'] == '教务') {
                $single_request_data['role_type'] = [2];
            } elseif ($teacher_info['role_name'] == '老师') {
                $single_request_data['role_type'] = [3];
            } else {
                $single_request_data['role_type'] = [3]; // 默认为老师
            }

            // 创建模拟的Request对象
            $mock_request = new \Illuminate\Http\Request($single_request_data);

            // 调用单个教师同步方法
            try {
                $result = $this->syncSingleTeacher($mock_request);
                $sync_results[] = array_merge($result, [
                    'teacher_name' => $teacher_info['teacher_name'],
                    'username' => $teacher_info['username']
                ]);

                if ($result['success']) {
                    if (isset($result['skipped']) && $result['skipped']) {
                        $skipped_count++;
                    } else {
                        $success_count++;
                    }
                } else {
                    $failed_count++;
                }
            } catch (\Exception $e) {
                $sync_results[] = [
                    'teacher_name' => $teacher_info['teacher_name'],
                    'username' => $teacher_info['username'],
                    'success' => false,
                    'message' => '同步失败: ' . $e->getMessage()
                ];
                $failed_count++;

                Log::warning('批量同步教师失败', [
                    'teacher_name' => $teacher_info['teacher_name'],
                    'username' => $teacher_info['username'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => true,
            'sync_results' => $sync_results,
            'total_count' => count($teachers),
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'skipped_count' => $skipped_count,
            'message' => "批量教师数据同步完成 - 成功: {$success_count}, 跳过: {$skipped_count}, 失败: {$failed_count}"
        ];
    }

    /**
     * 批量同步教师更新
     *
     * @param array $teachers
     * @return array
     */
    public function syncBatchTeachersUpdate(array $teachers): array
    {
        try {
            if (empty($teachers)) {
                return [
                    'success' => false,
                    'message' => '教师数据为空'
                ];
            }

            $sync_results = [];
            $success_count = 0;
            $failed_count = 0;

            foreach ($teachers as $teacher) {
                // 调用单个教师更新同步方法
                $result = $this->syncSingleTeacherUpdate($teacher);

                $sync_results[] = array_merge($result, [
                    'teacher_id' => $teacher->id,
                    'teacher_name' => $teacher->teacher_name ?? 'unknown'
                ]);

                if ($result['success']) {
                    $success_count++;
                } else {
                    $failed_count++;
                }
            }

            Log::info('批量教师更新同步完成', [
                'total_count' => count($teachers),
                'success_count' => $success_count,
                'failed_count' => $failed_count
            ]);

            return [
                'success' => true,
                'sync_results' => $sync_results,
                'total_count' => count($teachers),
                'success_count' => $success_count,
                'failed_count' => $failed_count,
                'message' => '批量教师更新同步完成'
            ];

        } catch (\Exception $e) {
            $this->throwBusinessException('批量教师更新同步失败');
        }
    }

    /**
     * 同步单个教师更新（保持原有逻辑）
     *
     * @param \App\Models\School\System\Teacher $teacher
     * @return array
     */
    public function syncSingleTeacherUpdate(\App\Models\School\System\Teacher $teacher): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 重新加载教师数据，确保包含最新的关联关系
            $teacher = \App\Models\School\System\Teacher::with(['user.roles', 'school', 'schoolCampus'])
                ->find($teacher->id);

            if (!$teacher || !$teacher->user) {
                return [
                    'success' => false,
                    'message' => '教师或用户信息不存在'
                ];
            }

            // 获取角色类型
            $roles = $teacher->user->roles->filter(function ($item) {
                return $item->status == 1;
            })->pluck('type')->unique()->toArray();

            // 查询角色ID
            $role_id = null;
            $role_source_id = null;

            if (in_array(2, $roles)) {
                // 教务角色
                $role = $this->getRoleInfo('教务', 2, $teacher->school_id);

                if ($role) {
                    $role_id = $role->id;
                    $role_source_id = '2';
                }
            } elseif (in_array(3, $roles) && !in_array(2, $roles)) {
                // 只有教师角色（没有教务）
                $role = $this->getRoleInfo('老师', 3, $teacher->school_id);

                if ($role) {
                    $role_id = $role->id;
                    $role_source_id = '3';
                }
            }

            if (!$role_id) {
                return [
                    'success' => false,
                    'message' => '未找到对应的角色信息'
                ];
            }

            // 根据学校ID+用户名查找现有的ysy_member记录
            $existing_member = $this->syncConnection->table('ysy_member')
                ->where('username', $teacher->user->username)
                ->where('school_id', $teacher->school_id)
                ->first();

            if (!$existing_member) {
                return [
                    'success' => false,
                    'message' => '未找到要更新的教师记录'
                ];
            }

            // 更新ysy_member表
            $member_update_data = [
                'name' => $teacher->teacher_name,
                'gender' => $teacher->user->gender == 1 ? 1 : 2,
                'role_id' => '0,' . $role_id . ',0',
                'role_source_id' => $role_source_id,
            ];

            $this->syncConnection->table('ysy_member')
                ->where('id', $existing_member->id)
                ->update($member_update_data);

            $synced_to_teacher = false;

            // 如果角色类型包含教师(type=3)，更新ysy_teacher表
            if (in_array(3, $roles)) {
                $existing_teacher = $this->syncConnection->table('ysy_teacher')
                    ->where('member_id', $existing_member->id)
                    ->first();

                if ($existing_teacher) {
                    // 更新现有教师记录
                    $teacher_update_data = [
                        'name' => $teacher->teacher_name,
                        'username' => $teacher->user->username,
                        'gender' => $teacher->user->gender == 1 ? 1 : 2,
                        'school_id' => $teacher->school_id,
                        'school_district' => $teacher->school_campus_id,
                        'step' => 0,
                        'type' => '老师',
                        'is_psych' => '0'
                    ];

                    $this->syncConnection->table('ysy_teacher')
                        ->where('id', $existing_teacher->id)
                        ->update($teacher_update_data);

                    $synced_to_teacher = true;
                } else {
                    // 如果ysy_teacher表中没有记录，则插入新记录
                    $teacher_insert_data = [
                        'member_id' => $existing_member->id,
                        'name' => $teacher->teacher_name,
                        'username' => $teacher->user->username,
                        'gender' => $teacher->user->gender == 1 ? 1 : 2,
                        'school_id' => $teacher->school_id,
                        'school_district' => $teacher->school_campus_id,
                        'step' => 0,
                        'type' => '老师',
                        'is_psych' => '0'
                    ];

                    $this->syncConnection->table('ysy_teacher')->insert($teacher_insert_data);
                    $synced_to_teacher = true;
                }
            }

            $this->syncConnection->commit();

            Log::info('教师更新同步成功', [
                'teacher_id' => $teacher->id,
                'member_id' => $existing_member->id,
                'username' => $teacher->user->username,
                'synced_to_teacher' => $synced_to_teacher
            ]);

            return [
                'success' => true,
                'teacher_id' => $teacher->id,
                'member_id' => $existing_member->id,
                'synced_to_member' => true,
                'synced_to_teacher' => $synced_to_teacher,
                'message' => '教师更新同步成功'
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            $this->throwBusinessException('单个教师更新同步失败');
        }
    }

    /**
     * 同步教务用户到老师接口
     * 通过机构ID获取学校ID，再获取校区ID进行同步
     *
     * @param User $user 教务用户
     * @param Request $request 请求对象（可选）
     * @return array
     */
    public function syncAdminUserIfNeeded($user, $request = null): array
    {
        try {
            // 教务用户同步，角色固定为教务（type=2）
            // 这里不需要检查角色，因为调用此方法的前提就是教务用户

            // 通过机构ID获取学校ID，再获取校区ID
            $schoolCampusId = null;

            try {
                // 通过用户的机构ID获取学校ID
                $organization_id = $user->organization_id;
               
                $school_id = $this->classService->getSchoolId(new \Illuminate\Http\Request(['organization_id' => $organization_id]));
               
                // 通过学校ID获取第一个可用的校区ID
                $schoolCampus = SchoolCampus::where('school_id', $school_id)
                    ->where('status', 1)
                    ->first();

                if ($schoolCampus) {
                    $schoolCampusId = $schoolCampus->id;
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::warning('获取校区ID失败', [
                    'user_id' => $user->id,
                    'organization_id' => $user->organization_id,
                    'error' => $e->getMessage()
                ]);
            }

            // 如果没有校区ID，跳过同步
            if (!$schoolCampusId) {
                \Illuminate\Support\Facades\Log::warning('教务用户同步跳过：缺少校区ID', [
                    'user_id' => $user->id,
                    'username' => $user->username
                ]);
                return [
                    'success' => false,
                    'message' => '缺少校区ID，无法同步',
                    'skipped' => true
                ];
            }
           
            // 准备同步到老师接口的数据
            $teacherSyncData = [
                'teacher_name' => $user->real_name,
                'username' => $user->username,
                'gender' => $user->gender ?? 1,
                'school_campus_id' => $schoolCampusId,
                'role_type' => [2], // 教务角色
            ];
            
            // 创建模拟的Request对象
            $mockRequest = new \Illuminate\Http\Request($teacherSyncData);

            // 调用单个教师同步方法
            $result = $this->syncSingleTeacher($mockRequest);
            // 记录同步结果
            \Illuminate\Support\Facades\Log::info('教务用户同步到老师接口', [
                'user_id' => $user->id,
                'username' => $user->username,
                'school_campus_id' => $schoolCampusId,
                'sync_result' => $result
            ]);

            return array_merge($result, [
                'message' => '教务用户同步成功'
            ]);

        } catch (\Exception $e) {
            $this->throwBusinessException('教务同步失败');
        }
    }

    /**
     * 转换性别字符串为数字
     *
     * @param string|int $gender 性别（男/女 或 1/2）
     * @return int 1=男，2=女
     */
    private function convertGenderToNumber($gender): int
    {
        if (is_numeric($gender)) {
            return (int)$gender;
        }

        return $gender === '女' ? 2 : 1;
    }

    /**
     * 获取角色信息（支持备用查询）
     *
     * @param string $roleName 角色名称
     * @param int $roleType 角色类型
     * @param int $schoolId 学校ID
     * @return object|null 返回角色信息，失败返回null
     */
    private function getRoleInfo(string $roleName, int $roleType, int $schoolId): ?object
    {
        // 先按名称查询
        $role = $this->syncConnection->table('ysy_role')
            ->where('name', $roleName)
            ->where('school_id', $schoolId)
            ->first();

        if (!$role) {
            // 如果没有找到指定名称的角色，则按type查询第一条记录
            $role = $this->syncConnection->table('ysy_role')
                ->where('type', $roleType)
                ->where('school_id', $schoolId)
                ->first();
        }

        return $role;
    }

    /**
     * 预处理教师角色信息（批量同步专用）
     *
     * @param int $schoolId 学校ID
     * @return array|null 返回角色信息，失败返回null
     */
    private function preprocessTeacherRoles(int $schoolId): ?array
    {
        try {
            $roleInfo = [];

            // 预处理教师角色
            $teacherRole = $this->getRoleInfo('老师', 3, $schoolId);
            if ($teacherRole) {
                $roleInfo['teacher_role_id'] = $teacherRole->id;
            }

            // 预处理教务角色
            $academicRole = $this->getRoleInfo('教务', 2, $schoolId);
            if ($academicRole) {
                $roleInfo['academic_role_id'] = $academicRole->id;
            }

            Log::info('预处理教师角色信息完成', [
                'school_id' => $schoolId,
                'teacher_role_id' => $roleInfo['teacher_role_id'] ?? null,
                'academic_role_id' => $roleInfo['academic_role_id'] ?? null
            ]);

            return $roleInfo;
        } catch (\Exception $e) {
            Log::error('预处理教师角色信息失败', [
                'school_id' => $schoolId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取或创建校区信息
     *
     * @param int $campusId 校区ID
     * @return array|null 返回校区和学校信息，失败返回null
     */
    private function getOrCreateDistrict(int $campusId): ?array
    {
        try {
            Log::info('查询校区信息', ['campus_id' => $campusId]);

            // 先查询 ysy_school_district 表中是否存在该校区
            $existingDistrict = $this->syncConnection->table('ysy_school_district')
                ->where('id', $campusId)
                ->first();

            Log::info('校区查询结果', [
                'found_district' => $existingDistrict ? true : false
            ]);

            if ($existingDistrict) {
                return [
                    'district_id' => $existingDistrict->id,
                    'school_id' => $existingDistrict->school_id
                ];
            }

            // 如果不存在，从 school_campuses 表获取校区信息
            $campus = DB::table('school_campuses')
                ->where('id', $campusId)
                ->first();

            if (!$campus) {
                Log::error('校区信息不存在', ['campus_id' => $campusId]);
                return null;
            }

            // 获取学校ID（通过现有的服务）
            $syncSchoolId = $this->schoolSyncService->getSyncDistrictId($campusId);
            if (!$syncSchoolId) {
                Log::error('无法获取同步学校ID', ['campus_id' => $campusId]);
                return null;
            }

            // 在 ysy_school_district 表中创建新校区
            $districtData = [
                'id' => $campusId, // 使用原校区ID作为新校区ID
                'name' => $campus->campus_name,
                'type' => $campus->type ?? 1,
                'school_id' => $syncSchoolId,
                'create_time' => now(),
                'step' => 0
            ];

            Log::info('准备创建校区', [
                'district_data' => $districtData
            ]);

            $this->syncConnection->table('ysy_school_district')->insert($districtData);

            Log::info('创建校区成功', [
                'district_id' => $campusId,
                'school_id' => $syncSchoolId,
                'campus_name' => $campus->campus_name
            ]);

            return [
                'district_id' => $campusId,
                'school_id' => $syncSchoolId
            ];

        } catch (\Exception $e) {
            Log::error('获取或创建校区失败', [
                'campus_id' => $campusId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}
