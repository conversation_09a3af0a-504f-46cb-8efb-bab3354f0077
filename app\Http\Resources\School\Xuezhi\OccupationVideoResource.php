<?php

namespace App\Http\Resources\School\Xuezhi;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OccupationVideoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'vid' => $this->vid,
            'xzq_id' => $this->xzq_id,
            'cover' => $this->cover,
            'occupation_type_id' => $this->occupation_type_id,
            'video_id' => (string) $this->video_id, // 强制字符串，避免前端数字精度丢失
            'occupation_type' => $this->whenLoaded('occupationType', function () {
                return [
                    'id' => $this->occupationType->id,
                    'occupation_name' => $this->occupationType->occupation_name,
                ];
            }),
            // 通过 video_id 衍生出的签名数据（详情接口中由 Service 注入）
            'video' => $this->when(isset($this->video), fn () => $this->video),
        ];
    }
}
