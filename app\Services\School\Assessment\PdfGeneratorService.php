<?php

namespace App\Services\School\Assessment;

use App\Services\BaseService;
use App\Services\CreatePdfFileService;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Models\School\Assessment\AssessmentComprehensivePdfUrl;
use Illuminate\Support\Facades\Config;

class PdfGeneratorService extends BaseService
{
    /**
     * 生成测评报告PDF
     *
     * @param int $assessment_id 测评ID
     * @param int $assessment_task_assignment_id 测评任务分配ID
     * @param bool $updateStatus 是否更新任务状态
     * @return string PDF URL
     * @throws \Exception
     */
    public function generatePdf(int $assessment_id, int $assessment_task_assignment_id, bool $updateStatus = true): string
    {
        // 获取测评ID与路径的映射关系
        $assessment_id_path_mapping = Config::get('assessment.assessment_id_path');
        $path = $assessment_id_path_mapping[$assessment_id] ?? null;
        
        if (!$path) {
            throw new \Exception("未找到测评ID对应的路径配置");
        }

        // 构建HTML URL
        $frontend_url = Config::get('app.frontend_url');
        $html_url = $frontend_url.'/survey/survey_report/'.$path.'?type=pdf&assessment_id=' . $assessment_id. '&assessment_task_assignment_id='. $assessment_task_assignment_id;

        // 创建PDF文件名
        $pdfFileName = '/assessment/pdf/' . date('Ymd') . '/' . $assessment_task_assignment_id . '-' . $assessment_id . '-' . time() . '.pdf';

        // 配置PDF生成参数
        $customDictConfig = [
            'path' => '/data/ysy/uploads_cdn' . $pdfFileName,
            'width' => '1000px',
            'height' => '1415px',
            'printBackground' => 'true',
        ];

        // 调用PDF生成服务
        $service = new CreatePdfFileService($html_url);
        $pdf_url = $service->createPdfFile($pdfFileName, $customDictConfig);

        // 更新任务状态
        if ($updateStatus) {
            $this->updatePdfStatus($assessment_task_assignment_id, $pdf_url);
        }

        return $pdf_url;
    }

    /**
     * 更新测评任务的PDF状态
     *
     * @param int $assessment_task_assignment_id 测评任务分配ID
     * @param string $pdf_url PDF URL
     * @return void
     */
    public function updatePdfStatus(int $assessment_task_assignment_id, string $pdf_url): void
    {
        // 更新测评状态，0未测评,1已测评未有结果,2有结果没有pdf_url,3有pdf_url
        AssessmentTaskAssignment::where('id', $assessment_task_assignment_id)
            ->update(['pdf_url' => $pdf_url, 'status' => 3]);
    }

    /**
     * 生成综合报告PDF
     *
     * @param int $user_id 用户ID
     * @param int $module career是生涯，competency是创新素养
     * @param bool $updateStatus 是否更新任务状态
     * @return string PDF URL
     * @throws \Exception
     */
    public function generateCompositePdf(int $user_id, int $module): string
    {
        // 构建HTML URL
        $frontend_url = Config::get('app.frontend_url');
        $html_url = $frontend_url.'/survey/survey_report/composite?type=pdf&user_id=' . $user_id. '&module='. $module;

        // 创建PDF文件名
        $pdfFileName = '/assessment/pdf_composite/' . date('Ymd') . '/' . $user_id . '-' . $module . '-' . time() . '.pdf';

        // 配置PDF生成参数
        $customDictConfig = [
            'path' => '/data/ysy/uploads_cdn' . $pdfFileName,
            'width' => '1000px',
            'height' => '1415px',
            'printBackground' => 'true',
        ];

        // 调用PDF生成服务
        $service = new CreatePdfFileService($html_url);
        $pdf_url = $service->createPdfFile($pdfFileName, $customDictConfig);

        // 更新任务状态
        AssessmentComprehensivePdfUrl::where('user_id', $user_id)
            ->update(['pdf_url' => $pdf_url, 'module' => $module]);
        
        return $pdf_url;
    }
}