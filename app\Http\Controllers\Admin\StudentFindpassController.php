<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\StudentFindpass;
use App\Models\StudentFindpassSmslog;
use App\Services\Admin\StudentFindpassService;
use App\Exceptions\BusinessException;
use Illuminate\Http\Request;

class StudentFindpassController extends Controller
{
    protected $studentFindpassService;

    public function __construct(StudentFindpassService $studentFindpassService)
    {
        $this->studentFindpassService = $studentFindpassService;
    }
    /**
     * 获取找回密码申请列表
     * 显示：学生姓名，用户名，学校名称，入学年份，班级，处理状态
     */
    public function index(Request $request)
    {
        try {
            $query = $this->studentFindpassService->getList($request);

            // 分页
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);

            $list = $query->select([
                'id',
                'student_name',
                'user_name',
                'school_name',
                'grade_year',
                'class_name',
                'check_status',
                'md5_password',
                'create_time',
                'check_time'
            ])
            ->orderBy('create_time', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

            return $this->success([
                'list' => $list->items(),
                'total' => $list->total(),
                'current_page' => $list->currentPage(),
                'per_page' => $list->perPage(),
                'last_page' => $list->lastPage(),
                'from' => $list->firstItem(),
                'to' => $list->lastItem(),
            ]);
        } catch (\Exception $e) {
            return $this->error('获取列表失败：' . $e->getMessage());
        }
    }

    /**
     * 处理找回密码申请
     * 管理员审核申请，可以通过或拒绝
     */
    public function process(Request $request, $id)
    {
        try {
            // 验证参数
            $request->validate([
                'check_status' => 'required|in:1,-1',
            ], [
                'check_status.required' => '处理状态不能为空',
                'check_status.in' => '处理状态只能是1(通过)或-1(拒绝)',
            ]);

            $adminUserId = $request->user()->id;
            $checkStatus = $request->input('check_status');

            $result = $this->studentFindpassService->processApplication($id, $adminUserId, $checkStatus, $request);

            return $this->success($result);

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        } catch (\Exception $e) {
            return $this->error('处理失败：' . $e->getMessage());
        }
    }




}
