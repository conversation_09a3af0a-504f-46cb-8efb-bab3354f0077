<?php

use App\Http\Controllers\GKOauthController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 权限管理相关模块路由
Route::group(['middleware' => ['auth.refresh']], function () {
    Route::get('v4_oauth', [GKOauthController::class, 'index']);

    // 当前登录用户菜单
    Route::get('user/menus', [UserController::class, 'getUserMenus'])->name('user.menus');
    // 当前登录用户信息
    Route::get('user/info', [UserController::class, 'getLoginUserInfo'])->name('user.info');
    // 当前登录用户详细信息（包含角色、班级等）
    Route::get('user/detail', [UserController::class, 'getCurrentUserDetail'])->name('user.detail');
    // 生成用户 Token
    Route::post('user/generate-token', [UserController::class, 'generateUserToken'])->name('user.generate-token');
    // 当前登录用户属于哪个机构的机构信息
    Route::get('user/orgInfo', [UserController::class, 'getLoginUserOrgInfo'])->name('user.orgInfo');
    // 登出
    Route::post('logout', [UserController::class, 'logOut'])->name('logOut');


    // 当前登录用户所属机构角色列表
    Route::get('roles', [RoleController::class, 'index'])->name('roles.list');
    // 新增角色
    Route::post('roles', [RoleController::class, 'store'])->name('roles.store');
    Route::put('roles/{id}', [RoleController::class, 'update'])->name('roles.update')->where('id', '[0-9]+');
    Route::get('roles/{id}', [RoleController::class, 'show'])->name('roles.show')->where('id', '[0-9]+');
    // 获取机构购买拥有的菜单列表
    Route::get('roles/getOrgHasMenus', [RoleController::class, 'getOrgHasMenus'])->name('roles.getOrgHasMenus');
    Route::get('roles/getOrgRoleTypes', [RoleController::class, 'getOrgRoleTypes'])->name('roles.getOrgRoleTypes');
    // 获取角色拥有的菜单
    Route::get('roles/{role_id}/getRoleHasMenus', [RoleController::class, 'getRoleHasMenus'])->name('roles.getRoleHasMenus')->where('role_id', '[0-9]+');
    // 设置角色拥有的菜单
    Route::post('roles/{role_id}/setRoleHasMenus', [RoleController::class, 'setRoleHasMenus'])->name('roles.setRoleHasMenus')->where('role_id', '[0-9]+');
    // 获取适用人群的默认菜单
    Route::get('roles/getCrowdDefaultMenus', [RoleController::class, 'crowdDefaultMenus'])->name('roles.getCrowdDefaultMenus');
    // 启用禁用角色
    Route::put('roles/{id}/updateRoleStatus', [RoleController::class, 'updateRoleStatus'])->name('roles.updateRoleStatus')->where('id', '[0-9]+');


    // 当前登录用户所属机构用户列表
    Route::get('users', [UserController::class, 'index'])->name('users.list');
    // 所属机构教务用户列表
    Route::get('edu_admins', [UserController::class, 'eduAdminList'])->name('users.eduAdminList');
    // 新增用户
    Route::post('users', [UserController::class, 'store'])->name('users.store');
    Route::get('users/{id}', [UserController::class, 'show'])->name('users.show')->where('id', '[0-9]+');
    Route::put('users/{id}', [UserController::class, 'update'])->name('users.update')->where('id', '[0-9]+');
    Route::delete('users/{id}', [UserController::class, 'destroy'])->name('users.delete')->where('id', '[0-9]+');
    Route::put('users/{id}/changeState', [UserController::class, 'changeState'])->name('users.changeState')->where('id', '[0-9]+');
    Route::post('users/{id}/resetPassword', [UserController::class, 'resetPassword'])->name('users.resetPassword')->where('id', '[0-9]+');
    Route::post('users/modifyPassword', [UserController::class, 'modifyPassword'])->name('users.modifyPassword');

    // 【远播教育】第三方创建用户接口
    Route::post('thirdParty/createUser', [UserController::class, 'createYbjyThirdPartyUser'])->name('thirdParty.createUser');

});

// 无需认证的用户相关接口
Route::group(['middleware' => ['access_log', 'throttle:100']], function () {
    // 根据openid获取用户登录信息
    Route::get('user/openid', [UserController::class, 'getUserByOpenid'])->name('user.openid');
});
