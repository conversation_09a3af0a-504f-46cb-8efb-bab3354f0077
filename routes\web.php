<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WeChatPlatformController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
Route::get('wechat/login', [WeChatPlatformController::class, 'login'])->name('wechat.login');
Route::get('wechat/callback', [WeChatPlatformController::class, 'callback'])->name('wechat.callback');

Route::get('/', function () {
    return view('welcome');
});

// 测试路由
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Laravel is working!',
        'timestamp' => now()
    ]);
});

// 直接在 web.php 中测试 evaluation 路由
Route::get('/evaluation/test-direct', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Direct evaluation route working!',
        'timestamp' => now()
    ]);
});
