<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

class SchoolAssessmentsSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';

    /**
     * 可选：单校执行时的学校ID
     */
    protected ?int $school_id = null;

    /**
     * survey_access 到 assessment_id 的映射
     * 1 => 1..8
     * 2 => 9..13
     * 3 => 14..20
     * 4 => [24]
     */
    private const DEFAULT_SURVEY_ACCESS_TO_ASSESSMENT_IDS = [
        1 => [1, 2, 3, 4, 5, 6, 7, 8],
        2 => [9, 10, 11, 12, 13],
        3 => [14, 15, 16, 17, 18, 19, 20],
        4 => [24],
    ];

    // 学校ID分组：不同学校范围使用不同的映射规则
    private const GROUP_A_SCHOOL_IDS = [
        122,123,126,163,178,179,226,230,247,276,277,278,295,298,304,334,340,454,466,
        499,500,501,505,549,550,698,761,824,842,843,860,862,864,884,885,886,887,889,
        890,891,893,911,912,916,917,939,942,943,944,946,948,950,952,953,955,956,958,
        960,961,962,964,965,966,967,971,975,976,977,979,980,981,985,988,989,992,993,
        994,995,996,997,998,1000,1001,1002,1003,1005,1006,1007,1008,1009,1011,1012,
        1016,1017,1018,1019,1020,1022,1023,1024,1026,1028,1047,1049,1050,1052,1053,
        1054,1056,1057,1058
    ];

    private const GROUP_B_SCHOOL_IDS = [503,517,973,987,999,1010,1015];

    private const GROUP_C_SCHOOL_IDS = [211,229,355,508,542,752,763,837,954,959,1013,1014,1021,1027];

    /**
     * 获取某学校的 survey_access 映射表
     */
    private function getMappingForSchool(int $schoolId): array
    {
        if (in_array($schoolId, self::GROUP_A_SCHOOL_IDS, true)) {
            // A组
            return [
                1 => [1, 2, 3, 4, 5],
                2 => [9, 10, 11, 12, 13],
                3 => [14, 15, 16, 17, 18],
                4 => [24],
            ];
        }
        if (in_array($schoolId, self::GROUP_B_SCHOOL_IDS, true)) {
            // B组
            return [
                1 => [6, 7, 8],
                2 => [9, 10, 11, 12, 13],
                3 => [14, 15, 16, 19, 20],
                4 => [24],
            ];
        }
        // C组或默认：使用完整映射（1..8 和 14..20）
        return self::DEFAULT_SURVEY_ACCESS_TO_ASSESSMENT_IDS;
    }

    public function __construct($schoolId = null)
    {
        if (!is_null($schoolId)) {
            $this->school_id = (int) $schoolId;
        }
    }

    public function run(): void
    {
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', '20000');

        $schools = $this->getTargetSchools();
        $hasIsOpenPuce = $this->hasIsOpenPuceColumn();

        $totalInserted = 0;
        foreach ($schools as $school) {
            $assessmentIds = $this->resolveAssessmentIdsFromSurveyAccess($school->id, $school->survey_access);
            if (empty($assessmentIds)) {
                continue;
            }

            // 去重并过滤已存在记录
            $assessmentIds = array_values(array_unique(array_filter($assessmentIds, fn($v) => is_numeric($v) && $v > 0)));
            if (empty($assessmentIds)) {
                continue;
            }

            $existing = DB::table('school_assessments')
                ->where('school_id', $school->id)
                ->pluck('assessment_id')
                ->toArray();

            $toInsert = array_values(array_diff($assessmentIds, $existing));
            if (empty($toInsert)) {
                continue;
            }

            $now = now();
            $batch = [];
            foreach ($toInsert as $aid) {
                $row = [
                    'school_id' => $school->id,
                    'assessment_id' => (int)$aid,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
                if ($hasIsOpenPuce) {
                    $row['is_open_puce'] = 1; // 默认不开启普测
                }
                $batch[] = $row;
            }

            DB::table('school_assessments')->insert($batch);
            $totalInserted += count($batch);

            Log::info('SchoolAssessmentsSeeder inserted for school', [
                'school_id' => $school->id,
                'inserted' => count($batch),
            ]);
        }

        Log::info('SchoolAssessmentsSeeder done', [
            'total_schools' => count($schools),
            'total_inserted' => $totalInserted,
        ]);
    }

    /**
     * 获取目标学校列表（来自老库），满足条件：step>=0 且 date_due>=今天
     * - 若构造函数传入 school_id，仅处理该校且需满足条件
     */
    protected function getTargetSchools()
    {
        $query = DB::connection($this->connect)
            ->table('school')
            ->select(['id', 'survey_access'])
            ->where('step', '>=', 0)
            ->where('date_due', '>=', date('Y-m-d'))
            ->orderBy('id', 'asc');

        if (!is_null($this->school_id)) {
            $query->where('id', $this->school_id);
        }

        return $query->get();
    }

    /**
     * 根据老库 school.survey_access 解析出应插入的 assessment_id 列表
     */
    protected function resolveAssessmentIdsFromSurveyAccess(int $schoolId, ?string $surveyAccess): array
    {
        $ids = [];

        // 老库字段默认可能是 '1,2'；若为空则按默认 '1,2'
        $value = trim((string)($surveyAccess ?? ''));
        if ($value === '') {
            $value = '1,2';
        }

        $mapping = $this->getMappingForSchool($schoolId);

        $parts = array_filter(array_map('trim', explode(',', $value)));
        foreach ($parts as $p) {
            $code = (int)$p;
            if (isset($mapping[$code])) {
                $ids = array_merge($ids, $mapping[$code]);
            }
        }

        return $ids;
    }

    /**
     * 检查 school_assessments 表是否存在 is_open_puce 字段
     */
    protected function hasIsOpenPuceColumn(): bool
    {
        try {
            return Schema::hasColumn('school_assessments', 'is_open_puce');
        } catch (\Throwable $e) {
            return false;
        }
    }
}

