<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QueryForgotPasswordStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_name' => 'required|string|max:255',
            'student_name' => 'required|string|max:255',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_name.required' => '用户名不能为空',
            'user_name.string' => '用户名必须是字符串',
            'user_name.max' => '用户名不能超过255个字符',
            'student_name.required' => '学生姓名不能为空',
            'student_name.string' => '学生姓名必须是字符串',
            'student_name.max' => '学生姓名不能超过255个字符',
        ];
    }
}
