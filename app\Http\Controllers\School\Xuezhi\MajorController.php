<?php

namespace App\Http\Controllers\School\Xuezhi;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Xuezhi\MajorRequest;
use App\Http\Resources\MajorAiIntroduction;
use App\Http\Resources\MajorCategoryResource;
use App\Http\Resources\MajorAcademicGroupResource;
use App\Http\Resources\MajorRankResource;
use App\Services\School\Xuezhi\MajorService;
use App\Traits\PaginationTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class MajorController extends Controller
{
    protected $connection = 'sqlsrv_gk';
    use PaginationTrait;

    protected MajorService $service;

    public function __construct(MajorService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取专业大小类
     * @return JsonResponse
     */
    public function category(): JsonResponse
    {
        $category = $this->service->category();

        return $this->success([
            'bk' => MajorCategoryResource::collection($category['BK'] ?? []),
            'zk' => MajorCategoryResource::collection($category['ZK'] ?? []),
        ]);
    }

    /**
     * 搜索专业，用于下拉专业列表
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function search(MajorRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $majorName = $validated['major_name'];

        $result = $this->service->searchByLinkName($majorName);

        return $this->success($result);
    }

    /**
     * 获取专业详情
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function info(MajorRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $majorCode = $validated['major_code'];
        $major = $this->service->info($majorCode);
        return $this->success($major);
    }

    /**
     * 获取排名前五的专业院校
     *
     * @param MajorRequest $request
     */
    public function top5MajorCollegeRank(MajorRequest $request): JsonResponse
    {
        //获取major_code
        $colleges= $this->service->top5MajorCollegeRank($request->major_code);
        return $this->success(MajorRankResource::collection($colleges));
    }

    /**
     * 获取专业开设院校
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function colleges(MajorRequest $request): JsonResponse
    {
        $query = $this->service->colleges($request);
        $type = $request['type'] ?? 1;

        // 当type=1时，查询使用了groupBy和聚合函数，需要特殊处理count
        if ($type == 1) {
            $countQuery = clone $query;
            $countQuery->orders = null; // 🔑 去掉排序，避免 SQL Server 报错
            $cnt = DB::connection($this->connection)->table(DB::connection($this->connection)->raw("({$countQuery->toSql()}) as sub"))
                    ->mergeBindings($countQuery) // 修正位置
                    ->count();

        } else {
            $cnt = $query->count();
        }

        $list = $this->scopePagination($query)->get();
        $list = $this->service->setTags($list);

        return $this->paginateSuccess($list, $cnt);
    }

    /**
     * 获取专业学术群组列表
     * @return JsonResponse
     */
    public function majorAcademicGroup(): JsonResponse
    {
        $data = $this->service->majorAcademicGroupList();
        return $this->success(MajorAcademicGroupResource::collection($data));
    }

    /**
     * 获取专业AI简介
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function aiIntroduction(MajorRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $aiIntroduction = $this->service->aiIntroduction($validated['code']);
        return $this->success(new MajorAiIntroduction($aiIntroduction));
    }

    /**
     * 获取就业走向
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function employment(MajorRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $employment = $this->service->employment($validated['major_code']);
        return $this->success($employment);
    }

    /**
     * 获取选科分布
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function xuankeDistributed(MajorRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $provinceId = $validated['province_id'];

        $xuankeDistributed = $this->service->xuankeDistributed($validated['major_name'], $provinceId);

        return $this->success($xuankeDistributed);
    }

    /**
     * 获取学职群
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function academic(MajorRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $academic = $this->service->academic($validated['major_code'], $validated['major_id']);

        return $this->success($academic);
    }

    /**
     * 根据vid和ts获取token
     *
     * @param Request $request
     */
    public function token(MajorRequest $request)
    {
        return $this->success($this->service->getToken($request->input('vid') ,$request->input('ts')));
    }

        /**
     * 获取专业详情
     *
     * @param MajorRequest $request
     * @return JsonResponse
     */
    public function analysis(MajorRequest $request): JsonResponse
    {
        $major = $this->service->analysis($request->input('major_code'));
        return $this->success($major);
    }

    /**
     * 学职群视频列表
     * 前端可传 academic_group_id（或AcademicGroupId）进行过滤；支持分页 page/page_size
     */
    public function academicGroupVideos(MajorRequest $request): JsonResponse
    {
        $query = $this->service->academicGroupVideosQuery($request);

        // 统计总数（避免 SQL Server group/order 影响）
        $countQuery = clone $query;
        $countQuery->orders = null;
        $total = DB::connection($this->connection)
            ->table(DB::connection($this->connection)->raw("({$countQuery->toSql()}) as sub"))
            ->mergeBindings($countQuery)
            ->count();

        $list = $this->scopePagination($query)->get();

        // 附加视频签名/时间戳，便于前端取token
        foreach ($list as &$item) {
            $ts = now()->timestamp;
            $item->video = [
                'ts' => $ts,
                'sign' => md5('hcTDd2tF9j' . $item->play_list_id . $ts),
            ];
        }

        return $this->paginateSuccess($list, $total);
    }


}
