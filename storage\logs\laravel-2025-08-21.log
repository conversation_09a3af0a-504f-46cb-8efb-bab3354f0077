[2025-08-21 08:53:10] local.ERROR: 旧密码错误 {"exception":"[object] (App\\Exceptions\\BusinessException(code: ): 旧密码错误 at E:\\YuanBoCode\\yishengya2024\\app\\Services\\BaseService.php:24)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(173): App\\Services\\BaseService->throwBusinessException('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#1 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}
"} 
[2025-08-21 08:53:26] local.ERROR: Too few arguments to function App\Exceptions\BusinessException::render(), 1 passed in E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php on line 455 and exactly 2 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Exceptions\\BusinessException::render(), 1 passed in E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php on line 455 and exactly 2 expected at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php:31)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(455): App\\Exceptions\\BusinessException->render(Object(Illuminate\\Http\\Request))
#1 E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php(46): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#4 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-08-21 08:55:22] local.ERROR: SQLSTATE[HY000]: General error: 1366 Incorrect integer value: '' for column 'user_id' at row 1 (Connection: mysql, SQL: insert into `model_change_logs` (`model_type`, `model_id`, `action`, `before`, `after`, `changed`, `user_id`, `user_name`, `ip_address`, `request_url`, `updated_at`, `created_at`) values (App\Models\User, 12089546, update, {"id":12089546,"organization_id":1,"username":"weiwentao","real_name":"\u9b4f\u6587\u6d9b","gender":"1","email":null,"email_verified_at":null,"password":null,"role_id":null,"openid":null,"phone":null,"status":1,"remember_token":null,"created_at":"2025-08-14T09:51:37.000000Z","updated_at":"2025-08-21T00:48:36.000000Z","deleted_at":null,"creator":"\u7ba1\u7406\u5458","updater":"\u9b4f\u6587\u6d9b","md5_password":"e10adc3949ba59abbe56e057f20f883e","wx_openid":"oni3S69rFjyC7NfBctWfPFgZ9Hv0","nickname":"Pluto."}, {"id":12089546,"organization_id":1,"username":"weiwentao","real_name":"\u9b4f\u6587\u6d9b","gender":"1","email":null,"email_verified_at":null,"password":"$2y$12$nw6fy.PrXWsTdj5.ht7xoO0Arq5LwDQJsHaUGb.jl20VH43Tn8n6i","role_id":null,"openid":null,"phone":null,"status":1,"remember_token":null,"created_at":"2025-08-14 17:51:37","updated_at":"2025-08-21 08:55:16","deleted_at":null,"creator":"\u7ba1\u7406\u5458","updater":"\u9b4f\u6587\u6d9b","md5_password":"e10adc3949ba59abbe56e057f20f883e","wx_openid":"oni3S69rFjyC7NfBctWfPFgZ9Hv0","nickname":"Pluto."}, {"password":"$2y$12$nw6fy.PrXWsTdj5.ht7xoO0Arq5LwDQJsHaUGb.jl20VH43Tn8n6i","updated_at":"2025-08-21 08:55:16"}, , , 127.0.0.1, http://dev.ysy.com/api/reset_password, 2025-08-21 08:55:16, 2025-08-21 08:55:16)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1366 Incorrect integer value: '' for column 'user_id' at row 1 (Connection: mysql, SQL: insert into `model_change_logs` (`model_type`, `model_id`, `action`, `before`, `after`, `changed`, `user_id`, `user_name`, `ip_address`, `request_url`, `updated_at`, `created_at`) values (App\\Models\\User, 12089546, update, {\"id\":12089546,\"organization_id\":1,\"username\":\"weiwentao\",\"real_name\":\"\\u9b4f\\u6587\\u6d9b\",\"gender\":\"1\",\"email\":null,\"email_verified_at\":null,\"password\":null,\"role_id\":null,\"openid\":null,\"phone\":null,\"status\":1,\"remember_token\":null,\"created_at\":\"2025-08-14T09:51:37.000000Z\",\"updated_at\":\"2025-08-21T00:48:36.000000Z\",\"deleted_at\":null,\"creator\":\"\\u7ba1\\u7406\\u5458\",\"updater\":\"\\u9b4f\\u6587\\u6d9b\",\"md5_password\":\"e10adc3949ba59abbe56e057f20f883e\",\"wx_openid\":\"oni3S69rFjyC7NfBctWfPFgZ9Hv0\",\"nickname\":\"Pluto.\"}, {\"id\":12089546,\"organization_id\":1,\"username\":\"weiwentao\",\"real_name\":\"\\u9b4f\\u6587\\u6d9b\",\"gender\":\"1\",\"email\":null,\"email_verified_at\":null,\"password\":\"$2y$12$nw6fy.PrXWsTdj5.ht7xoO0Arq5LwDQJsHaUGb.jl20VH43Tn8n6i\",\"role_id\":null,\"openid\":null,\"phone\":null,\"status\":1,\"remember_token\":null,\"created_at\":\"2025-08-14 17:51:37\",\"updated_at\":\"2025-08-21 08:55:16\",\"deleted_at\":null,\"creator\":\"\\u7ba1\\u7406\\u5458\",\"updater\":\"\\u9b4f\\u6587\\u6d9b\",\"md5_password\":\"e10adc3949ba59abbe56e057f20f883e\",\"wx_openid\":\"oni3S69rFjyC7NfBctWfPFgZ9Hv0\",\"nickname\":\"Pluto.\"}, {\"password\":\"$2y$12$nw6fy.PrXWsTdj5.ht7xoO0Arq5LwDQJsHaUGb.jl20VH43Tn8n6i\",\"updated_at\":\"2025-08-21 08:55:16\"}, , , 127.0.0.1, http://dev.ysy.com/api/reset_password, 2025-08-21 08:55:16, 2025-08-21 08:55:16)) at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `mo...', Array, Object(Closure))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `mo...', Array, Object(Closure))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `mo...', Array, 'id')
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `mo...', Array, 'id')
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ModelChangeLog))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\ModelChangeLog), Object(Closure))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(93): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(47): App\\Models\\User::writeChangeLog(Object(App\\Models\\User), 'update', Array, Array, Array)
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(458): App\\Models\\User::App\\Traits\\{closure}(Object(App\\Models\\User))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('eloquent.update...', Array)
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('eloquent.update...', Array, false)
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(215): Illuminate\\Events\\Dispatcher->dispatch('eloquent.update...', Array)
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1218): Illuminate\\Database\\Eloquent\\Model->fireModelEvent('updated', false)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1131): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#22 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#23 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#38 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1366 Incorrect integer value: '' for column 'user_id' at row 1 at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `mo...', Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `mo...', Array, Object(Closure))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `mo...', Array, Object(Closure))
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `mo...', Array, 'id')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `mo...', Array, 'id')
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ModelChangeLog))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\ModelChangeLog), Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(93): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(47): App\\Models\\User::writeChangeLog(Object(App\\Models\\User), 'update', Array, Array, Array)
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(458): App\\Models\\User::App\\Traits\\{closure}(Object(App\\Models\\User))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('eloquent.update...', Array)
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('eloquent.update...', Array, false)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(215): Illuminate\\Events\\Dispatcher->dispatch('eloquent.update...', Array)
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1218): Illuminate\\Database\\Eloquent\\Model->fireModelEvent('updated', false)
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1131): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#24 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#25 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#40 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 {main}
"} 
[2025-08-21 08:56:42] local.ERROR: 旧密码错误 {"exception":"[object] (App\\Exceptions\\BusinessException(code: ): 旧密码错误 at E:\\YuanBoCode\\yishengya2024\\app\\Services\\BaseService.php:24)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(173): App\\Services\\BaseService->throwBusinessException('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#1 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}
"} 
[2025-08-21 08:56:45] local.ERROR: Too few arguments to function App\Exceptions\BusinessException::render(), 1 passed in E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php on line 455 and exactly 2 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Exceptions\\BusinessException::render(), 1 passed in E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php on line 455 and exactly 2 expected at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php:31)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(455): App\\Exceptions\\BusinessException->render(Object(Illuminate\\Http\\Request))
#1 E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php(46): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#4 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-08-21 08:57:30] local.ERROR: 旧密码错误 {"exception":"[object] (App\\Exceptions\\BusinessException(code: ): 旧密码错误 at E:\\YuanBoCode\\yishengya2024\\app\\Services\\BaseService.php:24)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(173): App\\Services\\BaseService->throwBusinessException('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#1 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}
"} 
[2025-08-21 08:57:37] local.ERROR: Too few arguments to function App\Exceptions\BusinessException::render(), 1 passed in E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php on line 455 and exactly 2 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Exceptions\\BusinessException::render(), 1 passed in E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php on line 455 and exactly 2 expected at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php:31)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(455): App\\Exceptions\\BusinessException->render(Object(Illuminate\\Http\\Request))
#1 E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php(46): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#4 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-08-21 09:00:44] local.ERROR: SQLSTATE[HY000]: General error: 1366 Incorrect integer value: '' for column 'user_id' at row 1 (Connection: mysql, SQL: insert into `model_change_logs` (`model_type`, `model_id`, `action`, `before`, `after`, `changed`, `user_id`, `user_name`, `ip_address`, `request_url`, `updated_at`, `created_at`) values (App\Models\User, 12089546, update, {"id":12089546,"organization_id":1,"username":"weiwentao","real_name":"\u9b4f\u6587\u6d9b","gender":"1","email":null,"email_verified_at":null,"password":null,"role_id":null,"openid":null,"phone":null,"status":1,"remember_token":null,"created_at":"2025-08-14T09:51:37.000000Z","updated_at":"2025-08-21T00:56:44.000000Z","deleted_at":null,"creator":"\u7ba1\u7406\u5458","updater":"\u9b4f\u6587\u6d9b","md5_password":"e10adc3949ba59abbe56e057f20f883e","wx_openid":"oni3S69rFjyC7NfBctWfPFgZ9Hv0","nickname":"Pluto."}, {"id":12089546,"organization_id":1,"username":"weiwentao","real_name":"\u9b4f\u6587\u6d9b","gender":"1","email":null,"email_verified_at":null,"password":"$2y$12$OSBFZgVGl2pEs.\/VBeQgceujmxyzA\/YmCrNqXlhtVXB5c7iGdwwIa","role_id":null,"openid":null,"phone":null,"status":1,"remember_token":null,"created_at":"2025-08-14 17:51:37","updated_at":"2025-08-21 09:00:44","deleted_at":null,"creator":"\u7ba1\u7406\u5458","updater":"\u9b4f\u6587\u6d9b","md5_password":"e10adc3949ba59abbe56e057f20f883e","wx_openid":"oni3S69rFjyC7NfBctWfPFgZ9Hv0","nickname":"Pluto."}, {"password":"$2y$12$OSBFZgVGl2pEs.\/VBeQgceujmxyzA\/YmCrNqXlhtVXB5c7iGdwwIa","updated_at":"2025-08-21 09:00:44"}, , , 127.0.0.1, http://dev.ysy.com/api/reset_password, 2025-08-21 09:00:44, 2025-08-21 09:00:44)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1366 Incorrect integer value: '' for column 'user_id' at row 1 (Connection: mysql, SQL: insert into `model_change_logs` (`model_type`, `model_id`, `action`, `before`, `after`, `changed`, `user_id`, `user_name`, `ip_address`, `request_url`, `updated_at`, `created_at`) values (App\\Models\\User, 12089546, update, {\"id\":12089546,\"organization_id\":1,\"username\":\"weiwentao\",\"real_name\":\"\\u9b4f\\u6587\\u6d9b\",\"gender\":\"1\",\"email\":null,\"email_verified_at\":null,\"password\":null,\"role_id\":null,\"openid\":null,\"phone\":null,\"status\":1,\"remember_token\":null,\"created_at\":\"2025-08-14T09:51:37.000000Z\",\"updated_at\":\"2025-08-21T00:56:44.000000Z\",\"deleted_at\":null,\"creator\":\"\\u7ba1\\u7406\\u5458\",\"updater\":\"\\u9b4f\\u6587\\u6d9b\",\"md5_password\":\"e10adc3949ba59abbe56e057f20f883e\",\"wx_openid\":\"oni3S69rFjyC7NfBctWfPFgZ9Hv0\",\"nickname\":\"Pluto.\"}, {\"id\":12089546,\"organization_id\":1,\"username\":\"weiwentao\",\"real_name\":\"\\u9b4f\\u6587\\u6d9b\",\"gender\":\"1\",\"email\":null,\"email_verified_at\":null,\"password\":\"$2y$12$OSBFZgVGl2pEs.\\/VBeQgceujmxyzA\\/YmCrNqXlhtVXB5c7iGdwwIa\",\"role_id\":null,\"openid\":null,\"phone\":null,\"status\":1,\"remember_token\":null,\"created_at\":\"2025-08-14 17:51:37\",\"updated_at\":\"2025-08-21 09:00:44\",\"deleted_at\":null,\"creator\":\"\\u7ba1\\u7406\\u5458\",\"updater\":\"\\u9b4f\\u6587\\u6d9b\",\"md5_password\":\"e10adc3949ba59abbe56e057f20f883e\",\"wx_openid\":\"oni3S69rFjyC7NfBctWfPFgZ9Hv0\",\"nickname\":\"Pluto.\"}, {\"password\":\"$2y$12$OSBFZgVGl2pEs.\\/VBeQgceujmxyzA\\/YmCrNqXlhtVXB5c7iGdwwIa\",\"updated_at\":\"2025-08-21 09:00:44\"}, , , 127.0.0.1, http://dev.ysy.com/api/reset_password, 2025-08-21 09:00:44, 2025-08-21 09:00:44)) at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `mo...', Array, Object(Closure))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `mo...', Array, Object(Closure))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `mo...', Array, 'id')
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `mo...', Array, 'id')
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ModelChangeLog))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\ModelChangeLog), Object(Closure))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(93): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(47): App\\Models\\User::writeChangeLog(Object(App\\Models\\User), 'update', Array, Array, Array)
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(458): App\\Models\\User::App\\Traits\\{closure}(Object(App\\Models\\User))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('eloquent.update...', Array)
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('eloquent.update...', Array, false)
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(215): Illuminate\\Events\\Dispatcher->dispatch('eloquent.update...', Array)
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1218): Illuminate\\Database\\Eloquent\\Model->fireModelEvent('updated', false)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1131): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#22 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#23 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#38 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1366 Incorrect integer value: '' for column 'user_id' at row 1 at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `mo...', Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `mo...', Array, Object(Closure))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `mo...', Array, Object(Closure))
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `mo...', Array, 'id')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `mo...', Array, 'id')
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\ModelChangeLog))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(App\\Models\\ModelChangeLog), Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(93): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 E:\\YuanBoCode\\yishengya2024\\app\\Traits\\ModelChangeLogTrait.php(47): App\\Models\\User::writeChangeLog(Object(App\\Models\\User), 'update', Array, Array, Array)
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(458): App\\Models\\User::App\\Traits\\{closure}(Object(App\\Models\\User))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(286): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}('eloquent.update...', Array)
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(266): Illuminate\\Events\\Dispatcher->invokeListeners('eloquent.update...', Array, false)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(215): Illuminate\\Events\\Dispatcher->dispatch('eloquent.update...', Array)
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1218): Illuminate\\Database\\Eloquent\\Model->fireModelEvent('updated', false)
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1131): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#24 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#25 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#40 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 {main}
"} 
[2025-08-21 09:32:20] local.ERROR: Illuminate\Foundation\Exceptions\Handler::__construct(): Argument #1 ($container) must be of type Illuminate\Contracts\Container\Container, string given, called in E:\YuanBoCode\yishengya2024\app\Exceptions\BusinessException.php on line 29 {"exception":"[object] (TypeError(code: 0): Illuminate\\Foundation\\Exceptions\\Handler::__construct(): Argument #1 ($container) must be of type Illuminate\\Contracts\\Container\\Container, string given, called in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php on line 29 at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:154)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php(29): Illuminate\\Foundation\\Exceptions\\Handler->__construct('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF', NULL)
#1 E:\\YuanBoCode\\yishengya2024\\app\\Services\\BaseService.php(24): App\\Exceptions\\BusinessException->__construct('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF', NULL, NULL, NULL)
#2 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(173): App\\Services\\BaseService->throwBusinessException('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#3 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#18 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 {main}
"} 
[2025-08-21 09:32:59] local.ERROR: Illuminate\Foundation\Exceptions\Handler::__construct(): Argument #1 ($container) must be of type Illuminate\Contracts\Container\Container, string given, called in E:\YuanBoCode\yishengya2024\app\Exceptions\BusinessException.php on line 29 {"exception":"[object] (TypeError(code: 0): Illuminate\\Foundation\\Exceptions\\Handler::__construct(): Argument #1 ($container) must be of type Illuminate\\Contracts\\Container\\Container, string given, called in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php on line 29 at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:154)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php(29): Illuminate\\Foundation\\Exceptions\\Handler->__construct('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#1 E:\\YuanBoCode\\yishengya2024\\app\\Services\\BaseService.php(24): App\\Exceptions\\BusinessException->__construct('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF', NULL, NULL, NULL)
#2 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(173): App\\Services\\BaseService->throwBusinessException('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#3 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#18 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 {main}
"} 
[2025-08-21 09:33:26] local.ERROR: 旧密码错误 {"exception":"[object] (App\\Exceptions\\BusinessException(code: ): 旧密码错误 at E:\\YuanBoCode\\yishengya2024\\app\\Services\\BaseService.php:24)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\app\\Services\\UserService.php(173): App\\Services\\BaseService->throwBusinessException('\\xE6\\x97\\xA7\\xE5\\xAF\\x86\\xE7\\xA0\\x81\\xE9\\x94\\x99\\xE8\\xAF\\xAF')
#1 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Controllers\\UserController.php(327): App\\Services\\UserService->resetUserPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\UserController->resetPasswordNoLogin(Object(App\\Http\\Requests\\UserRequest))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('resetPasswordNo...', Array)
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\UserController), 'resetPasswordNo...')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#16 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 {main}
"} 
[2025-08-21 09:33:26] local.ERROR: Too few arguments to function App\Exceptions\BusinessException::render(), 1 passed in E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php on line 455 and exactly 2 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Exceptions\\BusinessException::render(), 1 passed in E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php on line 455 and exactly 2 expected at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\BusinessException.php:31)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(455): App\\Exceptions\\BusinessException->render(Object(Illuminate\\Http\\Request))
#1 E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php(46): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(App\\Exceptions\\BusinessException))
#4 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\CompressResponse.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CompressResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 E:\\YuanBoCode\\yishengya2024\\app\\Http\\Middleware\\AcceptHeader.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AcceptHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
