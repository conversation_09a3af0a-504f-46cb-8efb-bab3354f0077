<?php

namespace App\Http\Resources\School\Xuezhi;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 职业详情资源类
 * 
 * 该类用于格式化职业详情的响应数据
 */
class OccupationResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource['Id'] ?? $this->Id ?? null,
            'code' => $this->resource['Code'] ?? $this->Code ?? null,
            'parent_code' => $this->resource['ParentCode'] ?? $this->ParentCode ?? null,
            'occupation_name' => $this->resource['OccupationName'] ?? $this->OccupationName ?? null,
            'level_type' => $this->resource['LevelType'] ?? $this->LevelType ?? null,
            'description' => $this->resource['Description'] ?? $this->Description ?? null,
            'strategy' => $this->resource['Strategy'] ?? $this->Strategy ?? null,
            'click' => $this->resource['Click'] ?? $this->Click ?? null,
            'video_id' => $this->resource['VideoId'] ?? $this->VideoId ?? null,
            'video' => $this->when(isset($this->resource['video']) || isset($this->video), function () {
                return $this->resource['video'] ?? $this->video ?? null;
            }),
            'cases' => $this->when(isset($this->resource['cases']) || isset($this->cases), function () {
                $cases = $this->resource['cases'] ?? $this->cases ?? [];
                if (is_array($cases)) {
                    return collect($cases)->map(function ($case) {
                        return [
                            'id' => $case['Id'] ?? $case->Id ?? null,
                            'code' => $case['Code'] ?? $case->Code ?? null,
                            'case_name' => $case['CaseName'] ?? $case->CaseName ?? null,
                            'related_case' => $case['RelatedCase'] ?? $case->RelatedCase ?? null,
                        ];
                    });
                } else {
                    return $cases->map(function ($case) {
                        return [
                            'id' => $case->Id,
                            'code' => $case->Code,
                            'case_name' => $case->CaseName,
                            'related_case' => $case->RelatedCase,
                        ];
                    });
                }
            }),
        ];
    }
}