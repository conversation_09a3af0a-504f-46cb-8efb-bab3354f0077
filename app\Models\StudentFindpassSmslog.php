<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentFindpassSmslog extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'student_findpass_smslog';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'student_findpass_id',
        'admin_id',
        'admin_name',
        'operation_type',
        'operation_desc',
        'operation_time',
        'create_time',
        'ip_address',
        'user_agent',
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'operation_time' => 'datetime',
        'create_time' => 'datetime',
        'student_findpass_id' => 'integer',
        'admin_id' => 'integer',
    ];

    /**
     * 禁用 Laravel 默认的时间戳
     * 因为表使用 operation_time 字段
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 关联找回密码申请记录
     */
    public function findpassRecord()
    {
        return $this->belongsTo(StudentFindpass::class, 'student_findpass_id', 'id');
    }

    /**
     * 关联管理员用户
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id', 'id');
    }
}
