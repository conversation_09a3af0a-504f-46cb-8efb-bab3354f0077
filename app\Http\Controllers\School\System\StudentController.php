<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\System\StudentRequest;
use App\Models\School\System\Student;
use App\Services\School\System\StudentService;
use App\Services\DataSync\DataSyncService;
use App\Services\Tool\MessageService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StudentController extends Controller
{
    use CrudOperations;

    protected string $model = Student::class;

    protected $studentService;
    protected $dataSyncService;

    // 构造函数注入
    public function __construct(StudentService $studentService, DataSyncService $dataSyncService)
    {
        $this->studentService = $studentService;
        $this->dataSyncService = $dataSyncService;
    }

    public function index(Request $request)
    {
        $query = $this->studentService->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('students.id', 'desc')->get();
        return $this->paginateSuccess($list, $cnt);
    }

    public function show($id)
    {
        $record = Student::with([
            'school:id,name',
            'schoolCampus:id,campus_name',
            'lastClass.grade',
            'user.roles',
            'studentClasses' => function($query) {
                $query->with(['claass' => function($query) {
                    $query->with('grade:id,grade_name')
                          ->select('id', 'class_name', 'grade_id');
                }])->select('id', 'student_id', 'class_id', 'class_name', 'school_year', 'created_at');
            }
        ])->find($id);

        if (!$record) {
            return $this->notFound('查询对象不存在');
        }

        return $this->success($record);
    }

    // 新增学生
    public function store(StudentRequest $request)
    {
        $student = $this->studentService->store($request);

        // 调用同步接口
        try {
            $this->dataSyncService->syncSingleStudent($request);
        } catch (\Exception $e) {
            Log::warning('学生创建后同步失败', [
                'student_id' => $student->id ?? null,
                'error' => $e->getMessage()
            ]);
        }

        return $this->message('新增成功');
    }

    // 批量新增学生
    public function batchStore(StudentRequest $request)
    {
        $students = $this->studentService->batchStoreStudent($request);

        // 调用批量同步接口
        try {
            $this->dataSyncService->syncBatchStudents($request->all());
        } catch (\Exception $e) {
            Log::warning('学生批量创建后同步失败', [
                'student_count' => count($students),
                'error' => $e->getMessage()
            ]);
            
            // 内部开发，发送消息通知
            $message = [
                'title' => '学生批量创建后同步失败',
                'content' => '学生批量创建后同步失败，学生数量：' . count($students) . '，错误信息：' . $e->getMessage() ,
                'type' => 'error'
            ];
            $messageService = new MessageService();
            $messageService->sendMessage([12089545,12089546,12089547,12089548], $message);
        }

        return $this->message('批量新增学生成功');
    }

    // 学生批量升年级（自定义学生班级关系）
    public function batchUpgrade(StudentRequest $request)
    {
        $this->studentService->studentBatchUpgrade($request);
        return $this->message('批量升年级成功');
    }

    public function update(StudentRequest $request, $id)
    {
        $this->studentService->update($request, $id);
        return $this->message('更新成功');
    }

    public function destroy(Request $request, $id)
    {
        $this->studentService->destroy($request, $id);
        return $this->message('删除成功');
    }
    
    /**
     * 获取学生数据，用户学生列表穿梭框使用
     *
     * @return void
     */
    public function transferData(StudentRequest $request)
    {
        $data = $this->studentService->transferData($request);
        return $this->success($data);
    }
}
