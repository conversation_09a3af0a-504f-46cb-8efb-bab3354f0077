<?php

namespace App\Http\Controllers\School\Xuezhi;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Xuezhi\CollegeRequest;
use App\Http\Resources\CollegeCollection;
use App\Http\Resources\CollegeResource;
use App\Http\Resources\CollegeZhangChengCollection;
use App\Services\School\Xuezhi\CollegeService;
use App\Traits\PaginationTrait;
use Illuminate\Http\JsonResponse;

class CollegeController extends Controller
{
    use PaginationTrait;

    protected CollegeService $service;

    public function __construct(CollegeService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取院校列表
     * @param CollegeRequest $request
     * @return JsonResponse
     */
    public function index(CollegeRequest $request): JsonResponse
    {

        $query = $this->service->listBuilder($request);

        $total = $query->count();
        $colleges = CollegeCollection::collection(
            $this->scopePagination($query)->get()
        );
        
        return $this->paginateSuccess($colleges, $total);
    }

    /**
     * 获取院校详情
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $college = $this->service->show($id);
        $collegeResource = new CollegeResource($college);
        
        return $this->success($collegeResource);
    }

    /**
     * 根据院校id获取院校章程
     */
    public function zhangChengList($college_id)
    {
        $collegeZhangCheng = $this->service->zhangChengList($college_id);// 资源属性转换
        // 使用 CollegeResource 处理单个 College 对象
        $collegeZhangCheng= CollegeZhangChengCollection::collection($collegeZhangCheng);
        return $this->success($collegeZhangCheng);
    }

    /**
     * 根据章程id获取章程
     */
    public function zhangChengDetail($id)
    {
        $collegeZhangCheng = $this->service->zhangChengDetail($id);// 资源属性转换
        return $this->success($collegeZhangCheng);
    }

    /**
     * rank
     * 院校排名
     * @param CollegeRequest $request type 1:双一流名单 2:QS世界大学排名 3:全国学科评估 4:985大学名单 5:211大学名单 6世界大学学术榜7:特定类型排名
     */
    public function rank(CollegeRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $rankType = $validated['rank_type'] ?? "综合";
        $num = isset($validated['num']) ? (int)$validated['num'] : 4;
        $list = $this->service->rank($validated['type'], $rankType, $num);
        return $this->success($list);
    }

    /**
     * 就业走向接口
     */
    public function employment($id): JsonResponse
    {
        $employment = $this->service->employment($id);
        return $this->success($employment);
    }

}
