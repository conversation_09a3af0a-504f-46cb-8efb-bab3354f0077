<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ForgotPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => 'required|string|max:255',
            'real_name' => 'required|string|max:255',
            'captcha' => 'required|string',
            'key' => 'required|string',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.required' => '用户名不能为空',
            'username.string' => '用户名必须是字符串',
            'username.max' => '用户名不能超过255个字符',
            'real_name.required' => '真实姓名不能为空',
            'real_name.string' => '真实姓名必须是字符串',
            'real_name.max' => '真实姓名不能超过255个字符',
            'captcha.required' => '验证码不能为空',
            'captcha.string' => '验证码必须是字符串',
            'key.required' => '验证码key不能为空',
            'key.string' => '验证码key必须是字符串',
        ];
    }
}
