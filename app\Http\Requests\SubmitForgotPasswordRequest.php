<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SubmitForgotPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => 'required|string|max:255',
            'real_name' => 'required|string|max:255',
            'school_name' => 'required|string|max:255',
            'class_name' => 'required|string|max:255',
            'grade_year' => 'required|integer|min:1900|max:2100',
            'new_password' => 'required|string|min:6|max:255',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.required' => '用户名不能为空',
            'username.string' => '用户名必须是字符串',
            'username.max' => '用户名不能超过255个字符',
            'real_name.required' => '真实姓名不能为空',
            'real_name.string' => '真实姓名必须是字符串',
            'real_name.max' => '真实姓名不能超过255个字符',
            'school_name.required' => '学校名称不能为空',
            'school_name.string' => '学校名称必须是字符串',
            'school_name.max' => '学校名称不能超过255个字符',
            'class_name.required' => '班级名称不能为空',
            'class_name.string' => '班级名称必须是字符串',
            'class_name.max' => '班级名称不能超过255个字符',
            'grade_year.required' => '年级年份不能为空',
            'grade_year.integer' => '年级年份必须是数字',
            'grade_year.min' => '年级年份不能小于1900',
            'grade_year.max' => '年级年份不能大于2100',
            'new_password.required' => '新密码不能为空',
            'new_password.string' => '新密码必须是字符串',
            'new_password.min' => '新密码至少6位',
            'new_password.max' => '新密码不能超过255个字符',
        ];
    }
}
