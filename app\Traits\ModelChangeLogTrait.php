<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

/**
 * 模型变更日志 Trait
 * 
 * 该 Trait 为模型提供自动记录变更日志的功能，包括创建、更新和删除操作
 * 使用此 Trait 的模型将自动记录所有变更到 ModelChangeLog 表中
 */
trait ModelChangeLogTrait
{
    /**
     * 缓存旧数据，用于更新和删除操作
     * 格式：[模型主键 => 旧数据数组]
     */
    protected static $oldAttributesCache = [];

    /**
     * 注册模型事件监听器
     * 
     * @return void
     */
    protected static function bootModelChangeLogTrait()
    {
        // 监听新增事件
        static::created(function ($model) {
            self::writeChangeLog($model, 'create', null, $model->getAttributes(), null);
        });

        // 更新前缓存旧数据
        static::updating(function ($model) {
            self::$oldAttributesCache[$model->getKey()] = $model->getOriginal();
        });

        // 更新后写日志
        static::updated(function ($model) {
            $before = self::$oldAttributesCache[$model->getKey()] ?? [];
            unset(self::$oldAttributesCache[$model->getKey()]);

            $after = $model->getAttributes();
            $changedFields = array_keys($model->getChanges());

            self::writeChangeLog($model, 'update', $before, $after, $changedFields);
        });

        // 删除前缓存旧数据
        static::deleting(function ($model) {
            self::$oldAttributesCache[$model->getKey()] = $model->getOriginal();
        });

        // 删除后写日志
        static::deleted(function ($model) {
            $before = self::$oldAttributesCache[$model->getKey()] ?? [];
            unset(self::$oldAttributesCache[$model->getKey()]);

            self::writeChangeLog($model, 'delete', $before, null, null);
        });
    }

    /**
     * 写入日志表
     * 
     * @param \Illuminate\Database\Eloquent\Model $model 模型实例
     * @param string $action create|update|delete
     * @param array|null $before 修改前完整数据
     * @param array|null $after 修改后完整数据
     * @param array|null $changedFields 变更字段名数组，仅 update 有
     * @return void
     */
    protected static function writeChangeLog($model, $action, $before = null, $after = null, $changedFields = null)
    {
        $userId = Auth::id() ?? null;
        $username = Auth::user()->username ?? null;
        $ip = Request::ip();
        $url = Request::fullUrl();

        // 组装 changed 字段，存储变更字段名及其最新值
        $changed = null;
        if ($action === 'update' && !empty($changedFields) && is_array($changedFields)) {
            $changed = [];
            foreach ($changedFields as $field) {
                $changed[$field] = $after[$field] ?? null;
            }
        } elseif ($action === 'create' && !empty($after)) {
            // create 时 changed 直接存全部新增字段
            $changed = $after;
        }

        \App\Models\ModelChangeLog::create([
            'model_type' => get_class($model),
            'model_id' => $model->getKey(),
            'action' => $action,
            'before' => $before,
            'after' => $after,
            'changed' => $changed,
            'user_id' => $userId,
            'user_name' => $username,
            'ip_address' => $ip,
            'request_url' => $url,
        ]);
    }
}