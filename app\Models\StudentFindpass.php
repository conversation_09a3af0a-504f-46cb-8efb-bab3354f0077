<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentFindpass extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'student_findpass';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'student_name',
        'user_name',
        'school_name',
        'class_name',
        'grade_year',
        'password',
        'md5_password',
        'create_time',
        'check_status',
        'check_time',
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'create_time' => 'datetime',
        'check_time' => 'datetime',
        'grade_year' => 'integer',
        'check_status' => 'integer',
    ];

    /**
     * 禁用 Laravel 默认的时间戳
     * 因为表使用 create_time 字段
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 关联操作日志
     */
    public function smsLogs()
    {
        return $this->hasMany(StudentFindpassSmslog::class, 'findpass_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_name', 'username');
    }
}
