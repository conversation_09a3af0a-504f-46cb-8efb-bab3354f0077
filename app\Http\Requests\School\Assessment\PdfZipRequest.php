<?php

namespace App\Http\Requests\School\Assessment;

use App\Http\Requests\BaseRequest;

class PdfZipRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'batchDownloadReportPdf' => $this->batchDownloadReportPdfRules(),
            'generatePdf' => $this->generatePdfRules(),
            'generateCompositePdf' => $this->generateCompositePdfRules(),
            default => []
        };
    }

    private function batchDownloadReportPdfRules(): array
    {
        return [
            'assessment_schedule_id' => 'required|integer',
            'assessment_task_id' => 'required|integer',
            'assessment_id' => 'required|integer',
        ];
    }

    private function generatePdfRules(): array
    {
        return [
            'assessment_id' =>'required|integer',
            'assessment_task_assignment_id' => 'required|integer',
        ];
    }
    
    private function generateCompositePdfRules(): array
    {
        return [
            'user_id' =>'nullable|integer',
            'module' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'assessment_schedule_id.required' => '计划ID不能为空',
            'assessment_schedule_id.integer' => '计划ID必须为整数',
            'assessment_task_id.required' => '任务ID不能为空',
            'assessment_task_id.integer' => '任务ID必须为整数',
            'assessment_id.required' => '测评ID不能为空',
            'assessment_id.integer' => '测评ID必须为整数',
            'assessment_task_assignment_id.required' => '分发ID不能为空',
            'assessment_task_assignment_id.integer' => '分发ID必须为整数',
            'user_id.required' => '用户ID不能为空',
            'user_id.integer' => '用户ID必须为整数',
            'module.required' => '模块不能为空',
            'module.string' => '模块ID必须为字符串',
        ];
    }
}
