<?php

namespace App\Services\Tool;

use App\Models\User;
use App\Traits\DingDingMessage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;


class DingDingServer
{
        use DingDingMessage;
        //    测试环境
        protected $TestAgentId = '2660645194';
        protected $TestAppKey  = 'dingwda3x0fj2kddyjrz';
        protected $TestAppSecret = 'zESBUqm0JarKFOuYzioeBccqCbeCY6VKsbWM7undv4gydnlb8hexwsLJynp1HeFO';
        protected $TestRbootId = 'dingwda3x0fj2kddyjrz';

        //    正式环境
        protected $AgentId   = '2662243626';
        protected $appKey    = 'dingxm3fjvw3vfmghx6o';
        protected $appSecret = 'fTnBxhLZUqmvS1kQ5wcP9yhZ8GvXLWfLs5jULa-VjY8QhEzf1hgBcRzXtpUKsH7R';

        /**
         * 发送钉钉通知
         */
        function send_dingding_notification($data, $phone = '18611134083')
        {
                $dingUserId = $this->getUserIdByPhone($phone);

                return [
                        'userid'   => $dingUserId['userid'],
                        'response' => $this->sendCardNotice(
                                $dingUserId['userid'],
                                $data['remark'],
                                $data['title'],
                                $data['url'] ?? ''
                        ),
                        'phone'    => '18611134083'
                ];
        }

        public function getAccessToken()
        {
                //判断redis是否存在
                if (Redis::exists('dingdingaccesstoken')) {
                        $response = json_decode(Redis::get('dingdingaccesstoken'), true);
                } else {
                        $argv     = [
                                "appKey"    => $this->appKey,
                                "appSecret" => $this->appSecret
                        ];
                        $url      = 'https://api.dingtalk.com/v1.0/oauth2/accessToken';
                        $response = Http::withoutVerifying()
                                ->post($url, $argv)->json();
                        //将数据连同过期时间存储到redis
                        Redis::setex('dingdingaccesstoken', $response['expireIn'], json_encode($response));
                }
                return $response['accessToken'];
        }


        public function getTestAccessToken()
        {
                //判断redis是否存在
                if (Redis::exists('testdingdingaccesstoken')) {
                        $response = json_decode(Redis::get('testdingdingaccesstoken'), true);
                } else {
                        $argv     = [
                                "appKey"    => $this->TestAppKey,
                                "appSecret" => $this->TestAppSecret
                        ];
                        $url      = 'https://api.dingtalk.com/v1.0/oauth2/accessToken';
                        $response = Http::withoutVerifying()
                                ->post($url, $argv)->json();
                        //将数据连同过期时间存储到redis
                        Redis::setex('testdingdingaccesstoken', $response['expireIn'], json_encode($response));
                }
                return $response['accessToken'];
        }

        /**
         * 通过手机好获取用户id
         * https://open.dingtalk.com/document/orgapp/obtain-the-userid-of-your-mobile-phone-number
         * 接口文档地址
         *
         * @return void
         */
        public function getUserIdByPhone($phone)
        {
                $query    = [
                        'mobile' => $phone
                ];
                $token    = $this->getAccessToken();
                $url      = 'https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=' . $token;
                $response = Http::withoutVerifying()
                        ->asForm()
                        ->post($url, $query)->json();
                if (isset($response['result'])) {
                        return $response['result'];
                } else {
                        return ['userid' => '16302873922236991']; // 发给技术
                }
        }




        /**
         * 发送工作总通知
         * https://open.dingtalk.com/document/orgapp/asynchronous-sending-of-enterprise-session-messages
         * 接口文档地址
         *
         * @return void
         */
        public function sendTextNotice($user_id, $str = '')
        {
                $params   = [
                        'agent_id'    => $this->AgentId,
                        'userid_list' => $user_id,
                        'msg'         => json_encode([
                                'msgtype' => 'text',
                                'text'    => [
                                        'content' => $str
                                ]
                        ]),
                ];
                $token    = $this->getAccessToken();
                $url      = 'https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=' . $token;
                $response = Http::withoutVerifying()
                        ->asForm()
                        ->post($url, $params)->json();
                return $response;
        }

        /**
         * 发送工作总通知
         * https://open.dingtalk.com/document/orgapp/asynchronous-sending-of-enterprise-session-messages
         * 接口文档地址
         *
         * @return void
         */
        public function sendCardNotice($user_id, $str = '', $title = '通知', $url = 'https://saas.yishengya.cn/')
        {
                $arr      = [
                        "msgtype" => "oa",
                        "oa"      => [
                                "message_url"    => $url,
                                'pc_message_url' => sprintf(
                                        'dingtalk://dingtalkclient/page/link?url=%s&pc_slide=false&title=title',
                                        urlencode($url)
                                ),
                                "head"           => [
                                        "bgcolor" => "2b85e4",
                                        "text"    => "翼生涯"
                                ],
                                "body"           => [
                                        "title"   => $title,
                                        "content" => $str,
                                        "author"  => \request()->user()->name ?? "系统",
                                ]
                        ]
                ];
                $params   = [
                        'agent_id'    => $this->AgentId,
                        'userid_list' => $user_id,
                        'msg'         => json_encode($arr),
                ];
                $token    = $this->getAccessToken();
                $url      = 'https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=' . $token;
                $response = Http::withoutVerifying()
                        ->asForm()
                        ->post($url, $params)->json();
                return $response;
        }
}
