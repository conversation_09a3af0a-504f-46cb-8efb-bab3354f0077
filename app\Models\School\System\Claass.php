<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ModelChangeLogTrait;

class Claass extends BaseModel
{
    use SoftDeletes, ModelChangeLogTrait;

    protected $table = 'classes';

    // 归属于学校
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    // 归属于校区
    public function schoolCampus()
    {
        return $this->belongsTo(SchoolCampus::class, 'school_campus_id', 'id');
    }

    // 归属于年级
    public function grade()
    {
        return $this->belongsTo(Grade::class, 'grade_id', 'id');
    }

}
