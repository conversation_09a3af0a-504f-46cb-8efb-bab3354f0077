<?php

namespace App\Services\School\Assessment;

use App\Services\QwenService;
use App\Services\BaseService;

class MajorRecommendationService extends BaseService
{
    /**
     * 根据生涯测评结果生成专业推荐
     * 
     * @param string $interest 兴趣代码
     * @param string $personality 性格类型
     * @param string $advantages 优势智能
     * @param string $neutral 中性智能
     * @param string $disadvantages 劣势智能
     * @return array 专业推荐结果
     */
    public function generateMajorRecommendations($interest, $personality, $advantages, $neutral, $disadvantages)
    {
        $str = '我的生涯测评结果：兴趣是' . $interest . ' 
        性格是' . $personality . ' 
        优势智能是' . $advantages . '。
        中性智能是' . $neutral . '。 
        劣势智能是' . $disadvantages . '。 
        请根据我的生涯测评给我推荐8条专业方向及相应推荐理由和综合推荐指数（五星制）
        请返回json对象，不要空格，不要换行，不要转义字符，格式如下：
        [{"direction":"专业名称","reason":"简单描述推荐理由","level":"正整数数字"},{....}]
    
        直接返回推荐数据，不要做其他说明
        ';

        $service = new QwenService();
        $output = $service->qwenMax($str);
        $output_arr = json_decode($output, true); // 返回的数据是字符串，需要转成数组
        $text = json_decode($output_arr['output']['text'], true); // text是字符串，需要转成数组
        
        return $text;
    }
}