<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Services\School\Assessment\Score\ScoreServiceInterface;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Throwable;

class CalculateScoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    /**
     * Create a new job instance.
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    public function handle()
    {
        try {
            Log::info('开始处理评估答案和计算分数', ['params' => $this->params]);
            
            if (!$this->validateParams($this->params)) return;

            $scoreService = App::make(ScoreServiceInterface::class, [
                'assessment_id' => $this->params['assessment_id']
            ]);

            # 1. 计算分数
            $result = $scoreService->calculate($this->params);

            # 2. 更新任务结果
            $this->updateAssignmentResult($this->params['assessment_task_assignment_id'], $this->params['user_id'], $result);

            Log::info('职业评估分数计算完成', [
                'result' => $result
            ]);

            //当answer表录入完成，且assignments表standard_results字段更新后，添加生成PDF的任务
            // assessment_type为1,说明是任务制分发测评，才添加生成 PDF 的任务,24是学科兴趣，没有报告和pdf
            if ($this->params['assessment_type'] == 1 && $this->params['assessment_id'] < 24) {
                $this->addGeneratePdfJob($this->params);
            }

        } catch (\Exception $e) {
            throw new \Exception("职业评估分数计算失败", 500, $e);
        }
    }

    private function updateAssignmentResult(int $assignment_id, int $user_id, array $result): void
    {
        // 更新测评状态，0未测评,1已测评未有结果,2有结果没有pdf_url,3有pdf_url
        AssessmentTaskAssignment::where('id', $assignment_id)->update(['standard_results' => json_encode($result), 'user_id' => $user_id, 'status' => 2]);
    }
    
    private function addGeneratePdfJob(array $params): void
    {
        $job_pdf_params = [
            'school_id' => $params['school_id'],
            'assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
            'assessment_id' => $params['assessment_id'],
        ];

        \App\Jobs\GeneratePdfJob::dispatch($job_pdf_params)->onQueue('generate_pdf');
        
        // 生成综合报告PDF
        if(in_array($params['assessment_id'], [2,3,4])){
            $job_composite_pdf_params = [
                'school_id' => $params['school_id'],
                'user_id' => $params['user_id'],
                'module' => 'career',
            ];
            \App\Jobs\GenerateCompositePdfJob::dispatch($job_composite_pdf_params)->onQueue('generate_composite_pdf');
        }
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    protected function validateParams(array $params): bool
    {
        if (!is_numeric($params['school_id']) || !is_numeric($params['user_id']) || !is_numeric($params['assessment_id']) || !is_numeric($params['assessment_task_assignment_id'])) {
            Log::error('队列任务参数错误：学校id,用户id,测评id,分发id,必须是数字', ['params' => $params]);
            return false;
        }

        return true;
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        // 向用户发送失败通知等...
    }

}
