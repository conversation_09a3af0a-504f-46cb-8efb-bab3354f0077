<?php

namespace App\Services\School\Assessment\GroupReport\Career;

use App\Services\School\Assessment\GroupReport\AbstractGroupReportService;

/**
 * 生涯发展评估团体报告服务类
 * 
 * 该类用于生成生涯发展评估的团体报告，包括总体表现、具体表现和维度表现
 */
class DevelopmentService extends AbstractGroupReportService
{
    /**
     * 阈值分数常量
     * 
     * @var int
     */
    private const THRESHOLD_SCORE = 20;
    
    /**
     * 表现良好常量
     * 
     * @var string
     */
    private const PERFORMANCE_GOOD = 'good';
    
    /**
     * 表现不佳常量
     * 
     * @var string
     */
    private const PERFORMANCE_BAD = 'bad';

    /**
     * 维度类型常量
     * 
     * @var array
     */
    private const DIMENSION_TYPES = [
        'OVERALL' => 'total_score',
        'SPECIFIC' => 'specific',
        'DETAILED' => 'detailed'
    ];

    /**
     * 维度上层键名
     * 
     * @var array
     */
    protected $dimension_upper_keys = [
        'attitude',
        'cognition',
        'action'
    ];

    /**
     * 维度名称映射
     * 
     * @var array
     */
    protected $dimension_name_mapping = [
        'attitude' => '生涯态度',
        'cognition' => '生涯认知',
        'action' => '生涯行动'
    ];

    /**
     * 维度名称
     * 
     * @var array
     */
    protected $dimension_names = [
        '学习焦虑',
        '对人焦虑',
        '孤独倾向',
        '自责倾向',
        '过敏倾向',
        '身体症状',
        '恐怖倾向',
        '冲动倾向'
    ];
    
    /**
     * 生成报告数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    protected function generateReportData(
        array $filtered_assessment_with_results,
        array $class_statistics,
        array $params
    ): array {
        $dimensions = $this->getDimensionConfigs($params['assessment_id']);
        return [
            'participation_count' => $this->getMemberCount($class_statistics),
            'overall_performance' => $this->generateOverallPerformance($filtered_assessment_with_results),
            'specific_performance' => $this->generateSpecificPerformance($filtered_assessment_with_results, $class_statistics, $params['assessment_id'], $dimensions),
            'dimension_performance' => $this->generateDetailedPerformance($filtered_assessment_with_results, $class_statistics, $params['assessment_id'], $dimensions)
        ];
    }

    /**
     * 获取维度配置
     * 
     * @param int $assessmentId 评估ID
     * @return array 维度配置数据
     */
    private function getDimensionConfigs(int $assessmentId): array
    {
        return [
            'dimensions' => config('assessment.career.development_dimensions'),
            'sub_dimensions' => $assessmentId == 5
                ? config('assessment.career.development_dimensions_sub')
                : config('assessment.career.development_dimensions_sub_junior'),
            'sub_levels' => $assessmentId == 5
                ? config('assessment.career.development_dimension_sub_levels')
                : config('assessment.career.development_dimension_sub_levels_junior')
        ];
    }

    /**
     * 生成总体表现数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @return array 总体表现数据
     */
    private function generateOverallPerformance(array $filtered_assessment_with_results): array
    {
        $selected = $this->calculatePerformanceDistribution($filtered_assessment_with_results, self::DIMENSION_TYPES['OVERALL']);
        return [
            'header' => [
                'number' => 2,
                'name' => '生涯发展总体表现',
            ],
            'default' => [
                'axis' => ['生涯发展总体良好', '生涯发展总体有待提高'],
                'legend' => ['已选数据'],
                'series' => [array_values($selected)]
            ]
        ];
    }

    /**
     * 生成具体表现数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param int $assessment_id 评估ID
     * @param array $dimensions 维度数据
     * @return array 具体表现数据
     */
    private function generateSpecificPerformance(array $filtered_assessment_with_results, array $class_statistics, int $assessment_id, array $dimensions): array
    {
        $norm_scores = $this->getNormScores($assessment_id);
        $selected = $this->calculateDimensionAverages($filtered_assessment_with_results, self::DIMENSION_TYPES['SPECIFIC']);

        return [
            'header' => [
                'number' => 3,
                'name' => '生涯发展具体表现',
            ],
            'default' => $this->formatSpecificPerformanceData($dimensions['dimensions'], $norm_scores, $selected),
            'table' => $this->generatePerformanceTable($filtered_assessment_with_results, $dimensions['dimensions']),
            'class' => $this->generateClassPerformance($class_statistics, $dimensions['dimensions']),
            'class_table' => $this->generateClassPerformanceTable($class_statistics, $dimensions['dimensions'])
        ];
    }

    /**
     * 获取常模分数
     * 
     * @param int $assessment_id 评估ID
     * @return array 常模分数数据
     */
    private function getNormScores(int $assessment_id): array
    {
        return $assessment_id == 5
            ? [19.15, 18.47, 15.6]  // 高中常模
            : [20.33, 21.33, 20.14]; // 初中常模
    }

    /**
     * 计算表现分布
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param string $dimension_type 维度类型
     * @return array 表现分布数据
     */
    private function calculatePerformanceDistribution(array $all_assessment_with_results, string $dimension_type): array
    {
        $distribution = [
            self::PERFORMANCE_GOOD => 0,
            self::PERFORMANCE_BAD => 0
        ];

        foreach ($all_assessment_with_results as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
                
            if ($dimension_type === self::DIMENSION_TYPES['OVERALL']) {
                $score = $result['total_score'] ?? 0;
                if ($score >= self::THRESHOLD_SCORE) {
                    $distribution[self::PERFORMANCE_GOOD]++;
                } else {
                    $distribution[self::PERFORMANCE_BAD]++;
                }
            }
        }

        $total = count($all_assessment_with_results);
        return array_map(
            fn($value) => round($value / $total * 100, 2),
            $distribution
        );
    }

    /**
     * 格式化具体表现数据
     * 
     * @param array $dimensions 维度数据
     * @param array $norm_scores 常模分数
     * @param array $total 总体数据
     * @param array $selected 选中数据
     * @return array 格式化后的具体表现数据
     */
    private function formatSpecificPerformanceData(array $dimensions, array $norm_scores, array $selected): array
    {
        return [
            'axis' => $dimensions,
            'legend' => ['常模对比', '已选数据'],
            'series' => [
                array_values($norm_scores),
                array_values($selected)
            ]
        ];
    }

    /**
     * 生成表现表格数据
     * 
     * @param array $assessments 评估数据
     * @param array $dimensions 维度数据
     * @return array 表现表格数据
     */
    private function generatePerformanceTable(array $assessments, array $dimensions): array
    {
        $performance_data = $this->calculateDimensionPerformance($assessments, self::DIMENSION_TYPES['SPECIFIC']);
        $performance_data = $this->countToPercent($performance_data);
        return [
            'axis' => ['表现良好', '有待提高'],
            'legend' => $dimensions,
            'series' => array_values($performance_data)
        ];
    }
    
    /**
     * 生成班级表现数据
     * 
     * @param array $class_statistics 班级统计数据
     * @param array $dimensions 维度数据
     * @return array 班级表现数据
     */
    private function generateClassPerformance(array $class_statistics, array $dimensions): array
    {
        $class_performance = [];
        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_data = $class_statistics['category_data'][$class_name] ?? [];
            $averages = !empty($class_data)
                ? $this->calculateDimensionAverages($class_data, self::DIMENSION_TYPES['SPECIFIC'])
                : array_fill(0, count($dimensions), 0);

            $class_performance['axis'][] = $stats['class_name'];
            $class_performance['series'][$class_name] = array_values($averages);
        }

        $class_performance['legend'] = $dimensions;
        $class_performance['series'] = $this->reformatClassSeries($class_performance);

        return $class_performance;
    }

    /**
     * 生成班级表现表格数据
     * 
     * @param array $class_statistics 班级统计数据
     * @param array $dimensions 维度数据
     * @return array 班级表现表格数据
     */
    private function generateClassPerformanceTable(array $class_statistics, array $dimensions): array
    {
        $table_data = [
            'axis' => [],
            'legend' => $dimensions,
            'performance_labels' => ['表现良好', '有待提高'],
            'table' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $table_data['axis'][] = $stats['class_name'];
            $class_data = $class_statistics['category_data'][$class_name] ?? [];

            $table_data['table'][$class_name] = !empty($class_data)
                ? $this->formatClassPerformanceData($class_data, $dimensions)
                : array_map(fn() => ['/', '/'], $dimensions);
        }

        return $table_data;
    }

    /**
     * 生成详细表现数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param int $assessment_id 评估ID
     * @param array $dimensions 维度数据
     * @return array 详细表现数据
     */
    private function generateDetailedPerformance(array $filtered_assessment_with_results, array $class_statistics, int $assessment_id, array $dimensions): array
    {
        $norm_scores = $assessment_id == 5
            ? [18.74, 19.6, 19.66, 17.91, 17.89, 16.7, 16.82, 13.31]  // 高中常模
            : [19.85, 20.81, 21.54, 21.13, 19.89, 20.39];             // 初中常模

        $selected = $this->calculateDimensionAverages($filtered_assessment_with_results, self::DIMENSION_TYPES['DETAILED']);

        return [
            'header' => [
                'number' => 4,
                'name' => '生涯发展具体维度表现',
            ],
            'default' => [
                'axis' => $dimensions['sub_dimensions'],
                'legend' => ['常模对比', '已选数据'],
                'series' => [array_values($norm_scores), array_values($selected)]
            ],
            'proportions' => $this->generateDetailedProportions($filtered_assessment_with_results, $dimensions),
            'class_performance' => $this->generateDetailedClassPerformance($class_statistics, $dimensions),
            'class_proportions' => $this->generateDetailedClassProportions($class_statistics, $dimensions)
        ];
    }

    /**
     * 生成详细比例数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $dimensions 维度数据
     * @return array 详细比例数据
     */
    private function generateDetailedProportions(array $filtered_assessment_with_results, array $dimensions): array
    {
        $selected = $this->calculateDimensionPerformance($filtered_assessment_with_results, self::DIMENSION_TYPES['DETAILED']);

        $dimension_descriptions = [];
        foreach ($selected as $index => $data) {
            $good_percentage = round($data[self::PERFORMANCE_GOOD] / count($filtered_assessment_with_results) * 100, 2);
            $dimension_descriptions[] = sprintf(
                '在%s上，%s%%的学生表现良好。%s',
                $dimensions['sub_dimensions'][$index],
                $good_percentage,
                implode('', $dimensions['sub_levels'][$index][0])
            );
        }

        return [
            'descriptions' => $dimension_descriptions,
            'axis' => $dimensions['sub_dimensions'],
            'legend' => ['已选数据'],
            'performance_labels' => ['表现良好', '有待提高'],
            'series' => [
                $this->formatPerformancePercentages($selected, count($filtered_assessment_with_results))
            ]
        ];
    }

    /**
     * 生成详细班级表现数据
     * 
     * @param array $class_statistics 班级统计数据
     * @param array $dimensions 维度数据
     * @return array 详细班级表现数据
     */
    private function generateDetailedClassPerformance(array $class_statistics, array $dimensions): array
    {
        $class_performance = [
            'axis' => [],
            'legend' => $dimensions['sub_dimensions'],
            'series' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_performance['axis'][] = $stats['class_name'];
            $class_data = $class_statistics['category_data'][$class_name] ?? [];

            $averages = !empty($class_data)
                ? $this->calculateDimensionAverages($class_data, self::DIMENSION_TYPES['DETAILED'])
                : array_fill(0, count($dimensions['sub_dimensions']), 0);

            $class_performance['series'][$class_name] = array_values($averages);
        }

        $class_performance['series'] = $this->reformatClassSeries($class_performance);
        return $class_performance;
    }

    /**
     * 生成详细班级比例数据
     * 
     * @param array $class_statistics 班级统计数据
     * @param array $dimensions 维度数据
     * @return array 详细班级比例数据
     */
    private function generateDetailedClassProportions(array $class_statistics, array $dimensions): array
    {
        $table_data = [
            'axis' => [],
            'legend' => $dimensions['sub_dimensions'],
            'moyu' => ['表现良好', '有待提高'],
            'table' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $table_data['axis'][] = $stats['class_name'];
            $class_data = $class_statistics['category_data'][$class_name] ?? [];

            if (!empty($class_data)) {
                $performance_data = $this->calculateDimensionPerformance($class_data, self::DIMENSION_TYPES['DETAILED']);
                $table_data['table'][$class_name] = array_map(function ($counts) use ($class_data) {
                    return array_map(function ($count) use ($class_data) {
                        $percentage = round($count / count($class_data) * 100, 2);
                        return [$count, $percentage];
                    }, $counts);
                }, $performance_data);
            } else {
                $table_data['table'][$class_name] = array_fill(0, count($dimensions['sub_dimensions']), [0, 0]);
            }
        }

        return $table_data;
    }

    /**
     * 格式化表现百分比数据
     * 
     * @param array $performance_data 表现数据
     * @param int $total 总数
     * @return array 格式化后的表现百分比数据
     */
    private function formatPerformancePercentages(array $performance_data, int $total): array
    {
        return array_map(function ($counts) use ($total) {
            return array_map(
                fn($count) => round($count / $total * 100, 2),
                $counts
            );
        }, $performance_data);
    }

    /**
     * 计算维度平均值
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param string $dimension_type 维度类型
     * @return array 维度平均值数据
     */
    private function calculateDimensionAverages(array $all_assessment_with_results, string $dimension_type): array
    {
        $totals = [];
        $count = count($all_assessment_with_results);

        if ($count === 0) {
            return [];
        }

        foreach ($all_assessment_with_results as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);

            if ($dimension_type === self::DIMENSION_TYPES['DETAILED']) {
                // 处理详细维度 - 新结构中需要遍历每个父维度的子维度
                foreach ($result['dimensions'] as $parentDimension) {
                    foreach ($parentDimension['children'] as $dimension) {
                        $name = $dimension['name'];
                        $totals[$name] = ($totals[$name] ?? 0) + $dimension['score'];
                    }
                }
            } elseif ($dimension_type === self::DIMENSION_TYPES['SPECIFIC']) {
                // 处理特定维度 - 新结构中直接使用父维度的分数
                foreach ($result['dimensions'] as $dimension) {
                    $name = $dimension['name'];
                    $totals[$name] = ($totals[$name] ?? 0) + $dimension['score'];
                }
            }
        }

        return array_map(
            fn($total) => round($total / $count, 2),
            $totals
        );
    }

    /**
     * 重新格式化班级系列数据
     * 
     * @param array $data 数据
     * @return array 重新格式化后的班级系列数据
     */
    private function reformatClassSeries(array $data): array
    {
        $reformatted = [];
        foreach ($data['legend'] as $index => $dimension) {
            $reformatted[$index] = array_map(
                fn($class_name) => $data['series'][$class_name][$index] ?? 0,
                $data['axis']
            );
        }
        return $reformatted;
    }

    /**
     * 格式化班级表现数据
     * 
     * @param array $class_data 班级数据
     * @param array $dimensions 维度数据
     * @return array 格式化后的班级表现数据
     */
    private function formatClassPerformanceData(array $class_data, array $dimensions): array
    {
        $performance_data = $this->calculateDimensionPerformance($class_data, self::DIMENSION_TYPES['SPECIFIC']);
        $formatted_data = [];
        
        foreach ($dimensions as $dimension) {
            $dimension_name = $dimension;
            if (isset($performance_data[$dimension_name])) {
                $good_count = $performance_data[$dimension_name][self::PERFORMANCE_GOOD];
                $bad_count = $performance_data[$dimension_name][self::PERFORMANCE_BAD];
                $total = $good_count + $bad_count;

                $good_percentage = $total > 0 ? round($good_count / $total * 100, 2) : 0;
                $bad_percentage = $total > 0 ? round($bad_count / $total * 100, 2) : 0;
                
                $formatted_data[] = [
                    [$good_count, $good_percentage],
                    [$bad_count, $bad_percentage]
                ];
            } else {
                $formatted_data[] = [
                    [0, 0],
                    [0, 0]
                ];
            }
        }
        
        return $formatted_data;
    }

    /**
     * 计算维度表现
     * 
     * @param string $dimension_type 维度类型
     * @return array 维度表现数据
     */
    private function calculateDimensionPerformance(array $all_assessment_with_results, string $dimension_type): array
    {
        $performance = [];
        
        foreach ($all_assessment_with_results as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);

            if ($dimension_type === self::DIMENSION_TYPES['DETAILED']) {
                // 处理详细维度 - 新结构中需要遍历每个父维度的子维度
                foreach ($result['dimensions'] as $parentDimension) {
                    foreach ($parentDimension['children'] as $dimension) {
                        $score = $dimension['score'];
                        $code = $dimension['code'];
                        
                        if (!isset($performance[$code])) {
                            $performance[$code] = [
                                self::PERFORMANCE_GOOD => 0,
                                self::PERFORMANCE_BAD => 0
                            ];
                        }
                        
                        if ($score >= self::THRESHOLD_SCORE) {
                            $performance[$code][self::PERFORMANCE_GOOD]++;
                        } else {
                            $performance[$code][self::PERFORMANCE_BAD]++;
                        }
                    }
                }
            } elseif ($dimension_type === self::DIMENSION_TYPES['SPECIFIC']) {
                // 处理特定维度 - 新结构中直接使用父维度的分数
                foreach ($result['dimensions'] as $dimension) {
                    $score = $dimension['score'];
                    $name = $dimension['name'];
                    
                    if (!isset($performance[$name])) {
                        $performance[$name] = [
                            self::PERFORMANCE_GOOD => 0,
                            self::PERFORMANCE_BAD => 0
                        ];
                    }
                    
                    if ($score >= self::THRESHOLD_SCORE) {
                        $performance[$name][self::PERFORMANCE_GOOD]++;
                    } else {
                        $performance[$name][self::PERFORMANCE_BAD]++;
                    }
                }
            }
        }

        return $performance;
    }

    /**
     * 将个数转换为百分比
     * 
     * @param array $performance 各维度优劣个数数据
     * @return array 各维度优劣百分比数据
     */
    public function countToPercent(array $performance): array
    {
        foreach ($performance as $key => $item) {
            $good = $item[self::PERFORMANCE_GOOD];
            $bad = $item[self::PERFORMANCE_BAD];
            $sum = $good + $bad;
            $performance[$key][self::PERFORMANCE_GOOD] = $sum > 0 ? round($good / $sum * 100, 2) : 0;
            $performance[$key][self::PERFORMANCE_BAD] = $sum > 0 ? round($bad / $sum * 100, 2) : 0;
        }
        return $performance;
    }
}
