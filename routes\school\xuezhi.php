<?php

use App\Http\Controllers\School\Xuezhi\CollegeController;
use App\Http\Controllers\School\Xuezhi\DataQueryController;
use App\Http\Controllers\School\Xuezhi\MajorController;
use App\Http\Controllers\School\Xuezhi\OccupationController;
use App\Http\Controllers\School\Xuezhi\OccupationVideoController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 学校端 学职/院校库 相关模块路由
Route::group(['prefix' => 'college'], function () {
    Route::post('list', [CollegeController::class, 'index'])->name('college.list');
    Route::get('/{id}', [CollegeController::class, 'show'])->name('college.show')->where('id', '[0-9]+');
    Route::get('zhangChengList/{college_id}', [CollegeController::class, 'zhangChengList'])->name('college.zhangChengList')->where('college_id', '[0-9]+');
    Route::get('zhangCheng/{id}', [CollegeController::class, 'zhangChengDetail'])->name('college.zhangChengDetail')->where('id', '[0-9]+');

    Route::get('rank', [CollegeController::class, 'rank'])->name('college.rank');

    Route::get('employment/{college_id}', [CollegeController::class, 'employment'])->name('college.employment')->where('college_id', '[0-9]+');

});

// 学校端 学职/院校数据查询（小程序端接口（为了验证小程序端登录的用户，与pc端接口使用的中间件不一致，其他都一样））
Route::group(['prefix' => 'dataQuery', 'middleware' => ['api.aiUserCheck', 'xss.protection']], function () {
    //年份下拉
    Route::get('yearList', [DataQueryController::class, 'yearList'])->name('dataQuery.yearList');
    //科类下拉
    Route::get('liberalSciences', [DataQueryController::class, 'liberalSciences'])->name('dataQuery.liberalScience');
    //院校录取数据
    Route::get('collegeScore', [DataQueryController::class, 'collegeScore'])->name('dataQuery.collegeScore.list');
    //院校专业录取数据
    Route::get('majorScore', [DataQueryController::class, 'majorScore'])->name('dataQuery.majorScore.list');
    //院校专业计划录取数据
    Route::get('majorPlan', [DataQueryController::class, 'majorPlan'])->name('dataQuery.majorPlan.list');
});

// 学校端 学职/院校数据查询（pc端接口）
Route::group(['prefix' => 'pc/dataQuery', 'middleware' => ['auth.refresh']], function () {
    //年份下拉
    Route::get('yearList', [DataQueryController::class, 'yearList'])->name('dataQuery.yearList');
    //科类下拉
    Route::get('liberalSciences', [DataQueryController::class, 'liberalSciences'])->name('dataQuery.liberalScience');
    //院校录取数据
    Route::get('collegeScore', [DataQueryController::class, 'collegeScore'])->name('dataQuery.collegeScore.list');
    //院校专业录取数据
    Route::get('majorScore', [DataQueryController::class, 'majorScore'])->name('dataQuery.majorScore.list');
    //院校专业计划录取数据
    Route::get('majorPlan', [DataQueryController::class, 'majorPlan'])->name('dataQuery.majorPlan.list');
});

// 学校端 学职/院校库 相关模块路由
Route::group(['prefix' => 'major'], function () {
    //专业大小类
    Route::get('category', [MajorController::class, 'category'])->name('major.category.list');
    Route::get('search', [MajorController::class, 'search'])->name('major.category.search');
    //详情info
    Route::get('info', [MajorController::class, 'info'])->name('major.info');
    //就业走向employment
    Route::get('employment', [MajorController::class, 'employment'])->name('major.employment');
    Route::get('top5MajorCollegeRank', [MajorController::class, 'top5MajorCollegeRank'])->name('major.top5MajorCollegeRank');
    Route::get('colleges', [MajorController::class, 'colleges'])->name('major.colleges');
    Route::get('majorAcademicGroup', [MajorController::class, 'majorAcademicGroup'])->name('major.majorAcademicGroupList');
    Route::get('aiIntroduction', [MajorController::class, 'aiIntroduction'])->name('major.aiIntroduction');
    //选科xuankeDistributed
    Route::get('xuankeDistributed', [MajorController::class, 'xuankeDistributed'])->name('major.xuankeDistributed');
    //根据专业code查询学职群
    Route::get('academic', [MajorController::class, 'academic'])->name('major.academic');
    //根据vid ts获取token
    Route::get('token', [MajorController::class, 'token'])->name('major.token');
    Route::get('analysis', [MajorController::class, 'analysis'])->name('major.analysis');
    // 学职群视频列表
    Route::get('academicGroupVideos', [MajorController::class, 'academicGroupVideos'])->name('major.academicGroupVideos');

});

// 学校端 职业库 相关模块路由
Route::group(['prefix' => 'occupation'], function () {
    // 职业列表
    Route::get('list', [OccupationController::class, 'index'])->name('occupation.list');
    // 职业详情
    Route::get('/{id}', [OccupationController::class, 'show'])->name('occupation.show')->where('id', '[0-9]+');
});

// 学校端 职业视频 相关模块路由
Route::group(['prefix' => 'occupation-video'], function () {
    Route::get('list', [OccupationVideoController::class, 'index'])->name('occupation-video.list');
    Route::get('/{id}', [OccupationVideoController::class, 'show'])->name('occupation-video.show')->where('id', '[0-9]+');
});

// 学校端 职业类型 相关模块路由
Route::group(['prefix' => 'occupation-type'], function () {
    // 职业类型列表
    Route::get('list', [OccupationVideoController::class, 'occupationTypes'])->name('occupation-type.list');
    // 职业类型详情
    Route::get('/{id}', [OccupationVideoController::class, 'occupationTypeDetail'])->name('occupation-type.show')->where('id', '[0-9]+');
    // 职业类型及视频数量统计
    Route::get('with-count', [OccupationVideoController::class, 'occupationTypesWithCount'])->name('occupation-type.with-count');
});

