<?php

namespace App\Services\School\Xuezhi;

use App\Helpers\TextEncryptionHelper;
use App\Repositories\OccupationRepository;
use App\Services\BaseService;
use Illuminate\Database\Eloquent\Collection;

class OccupationService extends BaseService
{
    protected OccupationRepository $repository;

    public function __construct(OccupationRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 获取职业列表（按层级结构组织）
     *
     * @param string|null $occupationName 职业名称（模糊查询）
     * @return array 层级结构的职业列表
     */
    public function getOccupationList(?string $occupationName = null): array
    {
        // 从仓库获取职业列表
        $occupations = $this->repository->getOccupations($occupationName);

        // 如果是按名称搜索，直接返回结果，不需要层级结构
        if ($occupationName) {
            return $occupations->toArray();
        }

        // 构建层级结构
        return $this->buildHierarchicalStructure($occupations);
    }

    /**
     * 获取职业详情
     *
     * @param int $id 职业ID
     */
    public function getOccupationDetail(int $id)
    {
        // 从仓库获取职业详情
        $occupation = $this->repository->getOccupationById($id);

        if (!$occupation) {
            return null;
        }

        // 更新点击量
        $this->repository->incrementClick($occupation);

        // 对Description、Strategy以及案例的相关文本进行加密处理
        if ($occupation->Description) {
            $occupation->Description = TextEncryptionHelper::encryptText($occupation->Description);
        }
        if ($occupation->Strategy) {
            $occupation->Strategy = TextEncryptionHelper::encryptText($occupation->Strategy);
        }
        if ($occupation->cases) {
            foreach ($occupation->cases as $case) {
                $case->RelatedCase = TextEncryptionHelper::encryptText($case->RelatedCase);
            }
        }

        // 处理视频签名：根据 VideoId 生成加密参数并返回
        if (!empty($occupation->VideoId)) {
            $occupation->video = $this->getVideoEncrypt($occupation->VideoId);
        }

        // 构建返回数据
        return $occupation;
    }

    /**
     * 获取视频加密结果
     *
     * @param string $video_id 视频ID
     * @return array{sign:string,ts:int}
     */
    private function getVideoEncrypt($video_id): array
    {
        $secretkey = 'hcTDd2tF9j';
        $ts = now()->timestamp;
        $sign = md5($secretkey . $video_id . $ts);

        return [
            'sign' => $sign,
            'ts' => $ts,
        ];
    }

    /**
     * 构建职业的层级结构
     *
     * @param Collection $occupations 职业集合
     * @return array 层级结构的职业数据
     */
    private function buildHierarchicalStructure(Collection $occupations): array
    {
        // 按照 ParentCode 分组
        $grouped = $occupations->groupBy('ParentCode');

        // 获取顶级职业（ParentCode 为 null 或空的）
        $topLevel = $grouped->get(null, $grouped->get(''));

        if (!$topLevel) {
            return [];
        }

        // 递归构建层级结构
        return $this->buildChildren($topLevel, $grouped);
    }

    /**
     * 递归构建子职业
     *
     * @param Collection $items 当前层级的职业
     * @param Collection $grouped 按 ParentCode 分组的所有职业
     * @return array 构建好的层级结构
     */
    private function buildChildren(Collection $items, Collection $grouped): array
    {
        $result = [];

        foreach ($items as $item) {
            $data = $item->toArray();

            // 查找子职业
            $children = $grouped->get($item->Code);
            
            // 确保$children始终是Collection类型
            if ($children && !($children instanceof Collection)) {
                $children = collect([$children]);
            }

            if ($children && $children->count() > 0) {
                $data['children'] = $this->buildChildren($children, $grouped);
            }

            $result[] = $data;
        }

        return $result;
    }
}
