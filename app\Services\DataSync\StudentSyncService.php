<?php

namespace App\Services\DataSync;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StudentSyncService extends BaseService
{
    protected $syncConnection;
    protected $schoolSyncService;
    protected $classSyncService;

    public function __construct(SchoolSyncService $schoolSyncService, ClassSyncService $classSyncService)
    {
        $this->syncConnection = DB::connection('sync_mysql');
        $this->schoolSyncService = $schoolSyncService;
        $this->classSyncService = $classSyncService;
    }

    /**
     * 同步单个学生数据（保持原有逻辑）
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function syncSingleStudent($request): array
    {
        try {
            // 检查同步数据库连接
        

            // 获取请求参数
            $name = $request->input('student_name');
            $username = $request->input('username');
            $gender = $request->input('gender', 1);
            $class_id = $request->input('class_id'); // 单个学生传递class_id
            $class_name = $request->input('class_name'); // 批量学生传递class_name
            $grade_year = $request->input('grade_year');
            $init_grade_id = $request->input('init_grade_id');
            $school_campus_id = $request->input('school_campus_id');
            $school_no = $request->input('school_no');

           
           
            // 获取或创建校区信息
            $districtInfo = $this->getOrCreateDistrict($school_campus_id);
           
            if (!$districtInfo) {
                Log::error('获取或创建校区信息失败', ['school_campus_id' => $school_campus_id]);
                return [
                    'success' => false,
                    'message' => '获取或创建校区信息失败'
                ];
            }

            $syncDistrictId = $districtInfo['district_id'];
            $syncSchoolId = $districtInfo['school_id'];

         
            $ysy_grade = $this->syncConnection->table('ysy_grade')
                ->where('school_id', $syncSchoolId)
                ->where('name', $grade_year)
                ->where('grade_sort', $init_grade_id)
                ->first();
 
            if (!$ysy_grade) {
                // 如果没有找到年级，则创建新的年级
                $sync_grade_id = $this->createGrade($syncSchoolId, $grade_year, $init_grade_id, $syncDistrictId);
                if (!$sync_grade_id) {
                    Log::error('创建年级失败');
                    return [
                        'success' => false,
                        'message' => '创建年级失败'
                    ];
                }
                Log::info('年级创建成功', ['grade_id' => $sync_grade_id]);
            } else {
                $sync_grade_id = $ysy_grade->id;
                Log::info('找到现有年级', ['grade_id' => $sync_grade_id]);
            }

            // 根据传入参数决定如何获取class_name
            if ($class_id) {
                // 单个学生同步：通过class_id查询原表获取class_name
                $class = \DB::table('classes')->where('id', $class_id)->first();
                if (!$class) {
                    return [
                        'success' => false,
                        'message' => '未找到班级信息'
                    ];
                }
                $class_name = $class->class_name;
            }

            Log::info('查询班级信息', [
                'school_id' => $syncSchoolId,
                'grade_id' => $sync_grade_id,
                'class_name' => $class_name
            ]);

            $ysy_class = $this->syncConnection->table('ysy_class')
                ->where('school_id', $syncSchoolId)
                ->where('grade_id', $sync_grade_id)
                ->where('name', $class_name)
                ->first();

            Log::info('班级查询结果', [
                'found_class' => $ysy_class ? true : false
            ]);

            if (!$ysy_class) {
                Log::info('班级不存在，开始创建', [
                    'school_id' => $syncSchoolId,
                    'grade_id' => $sync_grade_id,
                    'class_name' => $class_name
                ]);

                // 如果没有找到班级，则创建新的班级
                $sync_class_id = $this->createClass($syncSchoolId, $sync_grade_id, $class_name, $syncDistrictId);
                if (!$sync_class_id) {
                    Log::error('创建班级失败');
                    return [
                        'success' => false,
                        'message' => '创建班级失败'
                    ];
                }
                Log::info('班级创建成功', ['class_id' => $sync_class_id]);
            } else {
                $sync_class_id = $ysy_class->id;
                Log::info('找到现有班级', ['class_id' => $sync_class_id]);
            }
           
            // 查询学生角色ID
            $role = $this->syncConnection->table('ysy_role')
                ->where('name', '学生')
                ->where('school_id', $syncSchoolId)
                ->first();
            if (!$role) {
                // 如果没有找到name='学生'的角色，则查询type=1的第一条记录
                $role = $this->syncConnection->table('ysy_role')
                    ->where('type', 1)
                    ->where('school_id', $syncSchoolId)
                    ->first();

                if (!$role) {
                    return [
                        'success' => false,
                        'message' => '未找到学生角色信息（name=学生 或 type=1）'
                    ];
                }
            }

            // 准备学生数据
            $student_data = [
                'name' => $name,
                'username' => $username,
                'password' => '827ccb0eea8a706c4c34a16891f84e7b',
                'role_id' => $role->id,
                'school_no' => $school_no,
                'school_id' => $syncSchoolId,
                'school_district' => $syncDistrictId, // 使用正确的校区ID
                'class_id' => $sync_class_id, // 使用同步数据库中的班级ID
                'grade_id' => $sync_grade_id, // 使用同步数据库中的年级ID
                'role_source_id' => '1', // 学生角色类型
                'step' => '0',
                'gender' => $gender
            ];
 
            // 调用syncStudent方法
            return $this->syncStudent($student_data);

        } catch (\Exception $e) {
            $this->throwBusinessException('同步单个学生数据失败');
        }
    }

    /**
     * 同步学生数据到ysy_member和ysy_student表
     * 
     * @param array $studentData 学生数据
     * @return array
     */
    public function syncStudent(array $studentData): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 检查ysy_member表中是否已存在相同用户名的记录
            $existingMember = $this->syncConnection->table('ysy_member')
                ->where('username', $studentData['username'])
                ->where('school_id', $studentData['school_id'])
                ->first();

            if ($existingMember) {
                // 如果member已存在，检查是否需要同步到student表
                $member_id = $existingMember->id;

                // 检查ysy_student表中是否已存在相同的记录
                $existingStudent = $this->syncConnection->table('ysy_student')
                    ->where('member_id', $member_id)
                    ->where('school_id', $studentData['school_id'])
                    ->first();

                if ($existingStudent) {
                    $this->syncConnection->rollBack();
                    return [
                        'success' => true,
                        'message' => '学生数据已存在，跳过同步',
                        'skipped' => true
                    ];
                }
            } else {
                // 准备同步到ysy_member表的数据
                $member_data = [
                    'name' => $studentData['name'],
                    'username' => $studentData['username'],
                    'password' => $studentData['password'],
                    'gender' => isset($studentData['gender']) ? ($studentData['gender'] == 1 ? 1 : 2) : 1,
                    'school_id' => $studentData['school_id'],
                    'role_id' => '0,' . ($studentData['role_id'] ?? '') . ',0',
                    'step' => 0,
                    'school_district' => $studentData['school_district'] ?? '',
                    'role_source_id' => '1',
                    'create_time' => now(),
                ];

                // 同步到ysy_member表并获取生成的ID
                Log::info('准备插入ysy_member表', [
                    'member_data' => $member_data
                ]);

                try {
                    $member_id = $this->syncConnection->table('ysy_member')->insertGetId($member_data);
                    Log::info('ysy_member插入成功', [
                        'member_id' => $member_id
                    ]);
                } catch (\Exception $e) {
                    Log::error('ysy_member插入失败', [
                        'error' => $e->getMessage(),
                        'member_data' => $member_data
                    ]);
                    throw $e;
                }
            }

            // 同步到ysy_student表
            $student_data = [
                'member_id' => $member_id,
                'name' => $studentData['name'],
                'username' => $studentData['username'],
                'student_no' => $studentData['school_no'] ?? '',
                'gender' => isset($studentData['gender']) ? ($studentData['gender'] == 1 ? 1 : 2) : 1,
                'school_id' => $studentData['school_id'],
                'school_district' => $studentData['school_district'],
                'class_id' => $studentData['class_id'] ?? '',
                'grade_id' => $studentData['grade_id'] ?? '',
                'step' => 0,
                'school_no' => $studentData['school_no'] ?? ''
            ];

            // 同步到ysy_student表
            Log::info('准备插入ysy_student表', [
                'student_data' => $student_data
            ]);

            try {
                $student_id = $this->syncConnection->table('ysy_student')->insertGetId($student_data);
                Log::info('ysy_student插入成功', [
                    'student_id' => $student_id
                ]);
            } catch (\Exception $e) {
                Log::error('ysy_student插入失败', [
                    'error' => $e->getMessage(),
                    'student_data' => $student_data
                ]);
                throw $e;
            }

            $this->syncConnection->commit();

            Log::info('学生数据同步成功', [
                'student_name' => $studentData['name'],
                'username' => $studentData['username'],
                'member_id' => $member_id,
                'student_id' => $student_id,
                'class_id' => $studentData['class_id'],
                'grade_id' => $studentData['grade_id']
            ]);

            return [
                'success' => true,
                'member_id' => $member_id,
                'student_id' => $student_id,
                'synced_to_member' => true,
                'synced_to_student' => true,
                'class_id' => $studentData['class_id'],
                'grade_id' => $studentData['grade_id'],
                'message' => '学生数据同步成功'
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            Log::error('学生数据同步失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->throwBusinessException('学生数据同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量同步学生数据（通过请求参数）
     *
     * @param \Illuminate\Http\Request $request 请求对象
     * @return array
     */
    public function syncBatchStudents($request_data): array
    {
        // 如果传入的是Request对象，转换为数组
        if (is_object($request_data) && method_exists($request_data, 'input')) {
            $students = $request_data->input('students', []);
            $school_campus_id = $request_data->input('school_campus_id');
            $grade_year = $request_data->input('grade_year');
            $init_grade_id = $request_data->input('init_grade_id');
        } else {
            // 如果传入的是数组
            $students = $request_data['students'] ?? [];
            $school_campus_id = $request_data['school_campus_id'] ?? null;
            $grade_year = $request_data['grade_year'] ?? null;
            $init_grade_id = $request_data['init_grade_id'] ?? null;
        }

        if (empty($students)) {
            return [
                'success' => false,
                'message' => '学生数据为空'
            ];
        }

        // 批量同步时，预先处理校区信息，避免重复查询
        $districtInfo = $this->getOrCreateDistrict($school_campus_id);
        if (!$districtInfo) {
            return [
                'success' => false,
                'message' => '获取或创建校区信息失败'
            ];
        }

        $syncDistrictId = $districtInfo['district_id'];
        $syncSchoolId = $districtInfo['school_id'];

        // 批量同步时，预先处理年级信息，避免重复查询
        $gradeInfo = $this->getOrCreateGrade($syncSchoolId, $init_grade_id, $syncDistrictId);
        if (!$gradeInfo) {
            return [
                'success' => false,
                'message' => '获取或创建年级信息失败'
            ];
        }

       

        // 预处理所有班级信息
        $classInfo = $this->preprocessAllClasses($students, $syncSchoolId, $gradeInfo['grade_id'], $syncDistrictId);
        if (!$classInfo) {
            return [
                'success' => false,
                'message' => '预处理班级信息失败'
            ];
        }

        // 查询学生角色信息
        $roleInfo = $this->getStudentRole($syncSchoolId);
        if (!$roleInfo) {
            return [
                'success' => false,
                'message' => '获取学生角色信息失败'
            ];
        }

        Log::info('批量同步预处理完成', [
            'campus_id' => $school_campus_id,
            'district_id' => $syncDistrictId,
            'school_id' => $syncSchoolId,
            'grade_id' => $gradeInfo['grade_id'],
            'class_count' => count($classInfo),
            'role_id' => $roleInfo['role_id'],
            'student_count' => count($students)
        ]);

        // 批量插入学生数据
        try {
            $result = $this->batchInsertStudents($students, $districtInfo, $gradeInfo, $classInfo, $roleInfo);
            return $result;
        } catch (\Exception $e) {
            Log::error('批量插入学生数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [
                'success' => false,
                'message' => '批量插入学生数据失败: ' . $e->getMessage()
            ];
        }

        return [
            'success' => true,
            'sync_results' => $sync_results,
            'total_count' => count($students),
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'skipped_count' => $skipped_count,
            'message' => "批量学生数据同步完成 - 成功: {$success_count}, 跳过: {$skipped_count}, 失败: {$failed_count}"
        ];
    }

    /**
     * 批量同步时使用的单个学生同步方法（避免重复校区和年级查询）
     *
     * @param array $studentData 学生数据
     * @param array $districtInfo 预处理的校区信息
     * @param array $gradeInfo 预处理的年级信息
     * @return array
     */
    private function syncSingleStudentWithPreprocessed(array $studentData, array $districtInfo, array $gradeInfo): array
    {
        try {
            $name = $studentData['student_name'];
            $username = $studentData['username'];
            $gender = $studentData['gender'];
            $class_name = $studentData['class_name'];
            $school_no = $studentData['school_no'];

            // 使用预处理的校区信息
            $syncDistrictId = $districtInfo['district_id'];
            $syncSchoolId = $districtInfo['school_id'];

            // 使用预处理的年级信息
            $sync_grade_id = $gradeInfo['grade_id'];

            // 查询或创建班级
            $ysy_class = $this->syncConnection->table('ysy_class')
                ->where('school_id', $syncSchoolId)
                ->where('grade_id', $sync_grade_id)
                ->where('name', $class_name)
                ->first();

            if (!$ysy_class) {
                $sync_class_id = $this->createClass($syncSchoolId, $sync_grade_id, $class_name, $syncDistrictId);
                if (!$sync_class_id) {
                    return [
                        'success' => false,
                        'message' => '创建班级失败'
                    ];
                }
            } else {
                $sync_class_id = $ysy_class->id;
            }

            // 查询学生角色
            $role = $this->syncConnection->table('ysy_role')
                ->where('name', '学生')
                ->where('school_id', $syncSchoolId)
                ->first();

            if (!$role) {
                $role = $this->syncConnection->table('ysy_role')
                    ->where('type', 1)
                    ->where('school_id', $syncSchoolId)
                    ->first();

                if (!$role) {
                    return [
                        'success' => false,
                        'message' => '未找到学生角色信息（name=学生 或 type=1）'
                    ];
                }
            }

            // 准备学生数据
            $student_data = [
                'name' => $name,
                'username' => $username,
                'password' => '827ccb0eea8a706c4c34a16891f84e7b',
                'role_id' => $role->id,
                'school_no' => $school_no,
                'school_id' => $syncSchoolId,
                'school_district' => $syncDistrictId,
                'class_id' => $sync_class_id,
                'grade_id' => $sync_grade_id,
                'role_source_id' => '1',
                'step' => '0',
                'gender' => $gender
            ];

            // 调用syncStudent方法
            Log::info('准备调用syncStudent方法', [
                'student_data' => $student_data
            ]);

            $result = $this->syncStudent($student_data);

            Log::info('syncStudent方法执行完成', [
                'result' => $result
            ]);

            // 记录所有SQL查询
            $allQueries = DB::connection('sync')->getQueryLog();
            Log::info('所有SQL查询记录', [
                'total_queries' => count($allQueries),
                'queries' => $allQueries
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error('同步学生数据异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '同步学生数据失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 转换性别字符串为数字
     *
     * @param string|int $gender 性别（男/女 或 1/2）
     * @return int 1=男，2=女
     */
    private function convertGenderToNumber($gender): int
    {
        if (is_numeric($gender)) {
            return (int)$gender;
        }

        return $gender === '女' ? 2 : 1;
    }

    /**
     * 创建或获取年级（与ClassSyncService保持一致的逻辑）
     *
     * @param int $schoolId 学校ID
     * @param string $gradeName 年级名称（这里实际是年份）
     * @param int $gradeSort 年级排序（init_grade_id）
     * @param int $schoolDistrict 校区ID（可选）
     * @return int|null 返回年级ID，失败返回null
     */
    private function createGrade(int $schoolId, string $gradeName, int $gradeSort, int $schoolDistrict = 0): ?int
    {
        try {

            // 根据年级排序推算年级信息（与ClassSyncService保持一致）
            $gradeInfo = $this->calculateGradeInfo($gradeSort);

            // 检查年级是否已存在
            $existingGrade = $this->syncConnection->table('ysy_grade')
                ->where('school_id', $schoolId)
                ->where('name', $gradeInfo['year'])           // 年份存储在name字段
                ->where('grade_name', $gradeInfo['grade_name']) // 年级名称存储在grade_name字段
                ->where('grade_sort', $gradeSort)
                ->first();

            if ($existingGrade) {
                return $existingGrade->id;
            }

            // 创建新年级
            $gradeData = [
                'school_id' => $schoolId,
                'school_district' => $schoolDistrict,
                'name' => $gradeInfo['year'],
                'grade_name' => $gradeInfo['grade_name'],
                'grade_sort' => $gradeSort,
                'create_time' => now(),
                'step' => 0
            ];

            $gradeId = $this->syncConnection->table('ysy_grade')->insertGetId($gradeData);

            Log::info('创建年级成功', [
                'school_id' => $schoolId,
                'calculated_year' => $gradeInfo['year'],
                'grade_name' => $gradeInfo['grade_name'],
                'grade_sort' => $gradeSort,
                'school_district' => $schoolDistrict,
                'grade_id' => $gradeId
            ]);

            return $gradeId;
        } catch (\Exception $e) {
            Log::error('创建年级失败', [
                'school_id' => $schoolId,
                'grade_name' => $gradeName,
                'grade_sort' => $gradeSort,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 创建新的班级（与ClassSyncService保持一致的逻辑）
     *
     * @param int $schoolId 学校ID
     * @param int $gradeId 年级ID
     * @param string $className 班级名称
     * @param int $schoolDistrict 校区ID（可选）
     * @return int|null 返回创建的班级ID，失败返回null
     */
    private function createClass(int $schoolId, int $gradeId, string $className, int $schoolDistrict = 0): ?int
    {
        try {

            // 同步班级信息 - 生成新的ID（与ClassSyncService保持一致）
            $syncClassData = [
                'name' => $className,
                'school_id' => $schoolId,
                'school_district' => $schoolDistrict,
                'grade_id' => $gradeId,
                'student_cnt' => '0',
                'create_time' => now(),
                'step' => 0
            ];

            // 使用insertGetId生成新的班级ID
            $classId = $this->syncConnection->table('ysy_class')->insertGetId($syncClassData);

            Log::info('创建班级成功', [
                'sync_class_id' => $classId,
                'school_id' => $schoolId,
                'grade_id' => $gradeId,
                'class_name' => $className,
                'school_district' => $schoolDistrict
            ]);

            return $classId;
        } catch (\Exception $e) {
            Log::error('创建班级失败', [
                'school_id' => $schoolId,
                'grade_id' => $gradeId,
                'class_name' => $className,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 计算年级信息（与ClassSyncService保持一致的逻辑）
     * 根据当前时间和年级推算入学年份
     *
     * @param int $grade 年级
     * @return array
     */
    private function calculateGradeInfo(int $grade): array
    {
        $currentYear = date('Y');
        $currentMonth = date('n');

        // 判断是否过了9月
        $isAfterSeptember = $currentMonth >= 9;

        // 年级名称映射
        $gradeNames = [
            7 => '初一',
            8 => '初二',
            9 => '初三',
            10 => '高一',
            11 => '高二',
            12 => '高三'
        ];

        // 计算入学年份
        if ($isAfterSeptember) {
            // 9月后：2025高一，2024高二，2023高三
            switch ($grade) {
                case 10: // 高一
                    $year = $currentYear;
                    break;
                case 11: // 高二
                    $year = $currentYear - 1;
                    break;
                case 12: // 高三
                    $year = $currentYear - 2;
                    break;
                case 7: // 初一
                    $year = $currentYear;
                    break;
                case 8: // 初二
                    $year = $currentYear - 1;
                    break;
                case 9: // 初三
                    $year = $currentYear - 2;
                    break;
                default:
                    $year = $currentYear;
            }
        } else {
            // 9月前：2024高一，2023高二，2022高三
            switch ($grade) {
                case 10: // 高一
                    $year = $currentYear - 1;
                    break;
                case 11: // 高二
                    $year = $currentYear - 2;
                    break;
                case 12: // 高三
                    $year = $currentYear - 3;
                    break;
                case 7: // 初一
                    $year = $currentYear - 1;
                    break;
                case 8: // 初二
                    $year = $currentYear - 2;
                    break;
                case 9: // 初三
                    $year = $currentYear - 3;
                    break;
                default:
                    $year = $currentYear - 1;
            }
        }

        return [
            'year' => (string)$year,
            'grade_name' => $gradeNames[$grade] ?? '未知年级'
        ];
    }

    /**
     * 获取或创建年级信息（批量同步专用，与单个学生添加逻辑一致）
     *
     * @param int $schoolId 学校ID
     * @param int $gradeSort 年级排序（init_grade_id）
     * @param int $schoolDistrict 校区ID
     * @return array|null 返回年级信息，失败返回null
     */
    private function getOrCreateGrade(int $schoolId, int $gradeSort, int $schoolDistrict): ?array
    {
        try {
            // 根据年级排序推算年级信息（与单个学生添加逻辑一致）
            $gradeInfo = $this->calculateGradeInfo($gradeSort);
            $calculatedYear = $gradeInfo['year'];
            $calculatedGradeName = $gradeInfo['grade_name'];

            // 先查询年级是否存在（使用推算出的信息）
            $ysy_grade = $this->syncConnection->table('ysy_grade')
                ->where('school_id', $schoolId)
                ->where('name', $calculatedYear)           // 使用推算的年份
                ->where('grade_name', $calculatedGradeName) // 使用推算的年级名称
                ->where('grade_sort', $gradeSort)
                ->first();

            if ($ysy_grade) {
                Log::info('找到现有年级', [
                    'grade_id' => $ysy_grade->id,
                    'calculated_year' => $calculatedYear,
                    'grade_name' => $calculatedGradeName,
                    'grade_sort' => $gradeSort
                ]);

                return [
                    'grade_id' => $ysy_grade->id,
                    'grade_name' => $ysy_grade->grade_name
                ];
            }

            // 如果不存在，创建新年级（使用推算的信息）
            $gradeId = $this->createGrade($schoolId, $calculatedYear, $gradeSort, $schoolDistrict);
            if (!$gradeId) {
                return null;
            }

            Log::info('创建新年级成功', [
                'grade_id' => $gradeId,
                'calculated_year' => $calculatedYear,
                'grade_name' => $calculatedGradeName,
                'grade_sort' => $gradeSort
            ]);

            return [
                'grade_id' => $gradeId,
                'grade_name' => $calculatedGradeName
            ];

        } catch (\Exception $e) {
            Log::error('获取或创建年级失败', [
                'school_id' => $schoolId,
                'grade_sort' => $gradeSort,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取或创建校区信息
     *
     * @param int $campusId 校区ID
     * @return array|null 返回校区和学校信息，失败返回null
     */
    private function getOrCreateDistrict(int $campusId): ?array
    {
        try {
            Log::info('查询校区信息', ['campus_id' => $campusId]);

            // 先查询 ysy_school_district 表中是否存在该校区
            $existingDistrict = $this->syncConnection->table('ysy_school_district')
                ->where('id', $campusId)
                ->first();

            Log::info('校区查询结果', [
                'found_district' => $existingDistrict ? true : false
            ]);

            if ($existingDistrict) {
                return [
                    'district_id' => $existingDistrict->id,
                    'school_id' => $existingDistrict->school_id
                ];
            }

            // 如果不存在，从 school_campuses 表获取校区信息
            $campus = DB::table('school_campuses')
                ->where('id', $campusId)
                ->first();

            if (!$campus) {
                Log::error('校区信息不存在', ['campus_id' => $campusId]);
                return null;
            }

            // 获取学校ID（通过现有的服务）
            $syncSchoolId = $this->schoolSyncService->getSyncDistrictId($campusId);
            if (!$syncSchoolId) {
                Log::error('无法获取同步学校ID', ['campus_id' => $campusId]);
                return null;
            }

            // 在 ysy_school_district 表中创建新校区
            $districtData = [
                'id' => $campusId, // 使用原校区ID作为新校区ID
                'name' => $campus->campus_name,
                'type' => $campus->type ?? 1,
                'school_id' => $syncSchoolId,
                'create_time' => now(),
                'step' => 0
            ];

            Log::info('准备创建校区', [
                'district_data' => $districtData
            ]);

            $this->syncConnection->table('ysy_school_district')->insert($districtData);

            Log::info('创建校区成功', [
                'district_id' => $campusId,
                'school_id' => $syncSchoolId,
                'campus_name' => $campus->campus_name
            ]);

            return [
                'district_id' => $campusId,
                'school_id' => $syncSchoolId
            ];

        } catch (\Exception $e) {
            Log::error('获取或创建校区失败', [
                'campus_id' => $campusId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 预处理所有班级信息
     *
     * @param array $students 学生数据数组
     * @param int $schoolId 学校ID
     * @param int $gradeId 年级ID
     * @param int $schoolDistrict 校区ID
     * @return array|null 返回班级信息映射，失败返回null
     */
    private function preprocessAllClasses(array $students, int $schoolId, int $gradeId, int $schoolDistrict): ?array
    {
        try {
            // 提取所有不同的班级名称
            $classNames = array_unique(array_column($students, 'class_name'));
            $classInfo = [];

            Log::info('开始预处理班级信息', [
                'school_id' => $schoolId,
                'grade_id' => $gradeId,
                'class_names' => $classNames,
                'total_classes' => count($classNames)
            ]);

            foreach ($classNames as $className) {
                if (empty($className)) {
                    Log::warning('发现空班级名称，跳过处理');
                    continue;
                }

                // 查询班级是否存在
                $ysy_class = $this->syncConnection->table('ysy_class')
                    ->where('school_id', $schoolId)
                    ->where('grade_id', $gradeId)
                    ->where('name', $className)
                    ->first();

                if ($ysy_class) {
                    $classInfo[$className] = $ysy_class->id;
                    Log::info('找到现有班级', [
                        'class_name' => $className,
                        'class_id' => $ysy_class->id
                    ]);
                } else {
                    // 创建新班级
                    Log::info('班级不存在，开始创建', [
                        'class_name' => $className,
                        'school_id' => $schoolId,
                        'grade_id' => $gradeId
                    ]);

                    $classId = $this->createClass($schoolId, $gradeId, $className, $schoolDistrict);
                    if (!$classId) {
                        Log::error('创建班级失败', [
                            'class_name' => $className,
                            'school_id' => $schoolId,
                            'grade_id' => $gradeId,
                            'school_district' => $schoolDistrict
                        ]);
                        return null;
                    }

                    $classInfo[$className] = $classId;
                    Log::info('创建班级成功', [
                        'class_name' => $className,
                        'class_id' => $classId
                    ]);
                }
            }

            Log::info('预处理班级信息完成', [
                'total_classes' => count($classNames),
                'processed_classes' => count($classInfo),
                'class_mapping' => $classInfo
            ]);

            return $classInfo;
        } catch (\Exception $e) {
            Log::error('预处理班级信息失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 获取学生角色信息
     *
     * @param int $schoolId 学校ID
     * @return array|null 返回角色信息，失败返回null
     */
    private function getStudentRole(int $schoolId): ?array
    {
        try {
            $role = $this->syncConnection->table('ysy_role')
                ->where('name', '学生')
                ->where('school_id', $schoolId)
                ->first();

            if (!$role) {
                // 如果没有找到name='学生'的角色，则查询type=1的第一条记录
                $role = $this->syncConnection->table('ysy_role')
                    ->where('type', 1)
                    ->where('school_id', $schoolId)
                    ->first();

                if (!$role) {
                    Log::error('未找到学生角色信息', ['school_id' => $schoolId]);
                    return null;
                }
            }

            return [
                'role_id' => $role->id
            ];
        } catch (\Exception $e) {
            Log::error('获取学生角色失败', [
                'school_id' => $schoolId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 批量插入学生数据
     *
     * @param array $students 学生数据数组
     * @param array $districtInfo 校区信息
     * @param array $gradeInfo 年级信息
     * @param array $classInfo 班级信息映射
     * @param array $roleInfo 角色信息
     * @return array
     */
    private function batchInsertStudents(array $students, array $districtInfo, array $gradeInfo, array $classInfo, array $roleInfo): array
    {
        $this->syncConnection->beginTransaction();

        try {
            $memberData = [];
            $studentData = [];
            $sync_results = [];
            $success_count = 0;
            $failed_count = 0;
            $skipped_count = 0;

            // 准备批量插入的member数据
            foreach ($students as $index => $student_info) {
                $username = $student_info['username'];
                $name = $student_info['student_name'];
                $gender = $this->convertGenderToNumber($student_info['gender'] ?? '男');
                $school_no = $student_info['school_no'] ?? '';
                $class_name = $student_info['class_name'];

                // 验证班级信息是否存在
                if (!isset($classInfo[$class_name])) {
                    Log::error('班级信息不存在', [
                        'student_name' => $name,
                        'class_name' => $class_name,
                        'available_classes' => array_keys($classInfo)
                    ]);
                    $sync_results[] = [
                        'student_name' => $name,
                        'username' => $username,
                        'success' => false,
                        'message' => "班级信息不存在: {$class_name}"
                    ];
                    $failed_count++;
                    continue;
                }

                // 检查是否已存在
                $existingMember = $this->syncConnection->table('ysy_member')
                    ->where('username', $username)
                    ->where('school_id', $districtInfo['school_id'])
                    ->first();

                if ($existingMember) {
                    $sync_results[] = [
                        'student_name' => $name,
                        'username' => $username,
                        'success' => true,
                        'skipped' => true,
                        'message' => '用户已存在，跳过'
                    ];
                    $skipped_count++;
                    continue;
                }

                // 准备member数据
                $memberData[] = [
                    'name' => $name,
                    'username' => $username,
                    'password' => '827ccb0eea8a706c4c34a16891f84e7b',
                    'gender' => $gender,
                    'school_id' => $districtInfo['school_id'],
                    'role_id' => '0,' . $roleInfo['role_id'] . ',0',
                    'step' => 0,
                    'school_district' => $districtInfo['district_id'],
                    'role_source_id' => '1',
                    'create_time' => now(),
                    'index' => $index // 用于关联student数据
                ];

                // 准备student数据（暂时不包含member_id）
                $studentData[] = [
                    'name' => $name,
                    'username' => $username,
                    'student_no' => $school_no,
                    'gender' => $gender,
                    'school_id' => $districtInfo['school_id'],
                    'school_district' => $districtInfo['district_id'],
                    'class_id' => $classInfo[$class_name],
                    'grade_id' => $gradeInfo['grade_id'],
                    'step' => 0,
                    'school_no' => $school_no,
                    'index' => $index // 用于关联member数据
                ];

                $sync_results[] = [
                    'student_name' => $name,
                    'username' => $username,
                    'success' => true,
                    'message' => '准备插入'
                ];
                $success_count++;
            }

            // 批量插入member数据
            if (!empty($memberData)) {
                // 移除index字段用于插入
                $memberInsertData = array_map(function($item) {
                    unset($item['index']);
                    return $item;
                }, $memberData);

                $this->syncConnection->table('ysy_member')->insert($memberInsertData);

                // 获取插入的member IDs
                $memberIds = [];
                foreach ($memberData as $member) {
                    $insertedMember = $this->syncConnection->table('ysy_member')
                        ->where('username', $member['username'])
                        ->where('school_id', $member['school_id'])
                        ->first();

                    if ($insertedMember) {
                        $memberIds[$member['index']] = $insertedMember->id;
                    }
                }

                // 更新student数据，添加member_id
                $studentInsertData = [];
                foreach ($studentData as $student) {
                    if (isset($memberIds[$student['index']])) {
                        $student['member_id'] = $memberIds[$student['index']];
                        unset($student['index']);
                        $studentInsertData[] = $student;
                    }
                }

                // 批量插入student数据
                if (!empty($studentInsertData)) {
                    $this->syncConnection->table('ysy_student')->insert($studentInsertData);
                }
            }

            $this->syncConnection->commit();

            Log::info('批量插入学生数据成功', [
                'total_count' => count($students),
                'success_count' => $success_count,
                'skipped_count' => $skipped_count,
                'failed_count' => $failed_count
            ]);

            return [
                'success' => true,
                'sync_results' => $sync_results,
                'total_count' => count($students),
                'success_count' => $success_count,
                'failed_count' => $failed_count,
                'skipped_count' => $skipped_count,
                'message' => "批量学生数据同步完成 - 成功: {$success_count}, 跳过: {$skipped_count}, 失败: {$failed_count}"
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            throw $e;
        }
    }

    /**
     * 从班级ID获取年级信息
     *
     * @param int $classId 班级ID
     * @return array|null 返回班级年级信息，失败返回null
     */
    private function getClassGradeInfo(int $classId): ?array
    {
        try {
            // 从classes表获取班级信息，关联grades表获取年级信息
            $classInfo = DB::table('classes')
                ->join('grades', 'classes.grade_id', '=', 'grades.id')
                ->where('classes.id', $classId)
                ->select(
                    'classes.class_name',
                    'grades.grade_name',
                    'grades.id as grade_id'
                )
                ->first();

            if (!$classInfo) {
                Log::error('班级信息不存在', ['class_id' => $classId]);
                return null;
            }

            // 根据年级名称推算init_grade_id和grade_year
            $gradeMapping = [
                '初一' => 7, '初二' => 8, '初三' => 9,
                '高一' => 10, '高二' => 11, '高三' => 12
            ];

            $init_grade_id = $gradeMapping[$classInfo->grade_name] ?? null;
            if (!$init_grade_id) {
                Log::error('无法识别年级名称', [
                    'class_id' => $classId,
                    'grade_name' => $classInfo->grade_name
                ]);
                return null;
            }

            // 根据init_grade_id推算grade_year
            $gradeInfo = $this->calculateGradeInfo($init_grade_id);

            return [
                'class_name' => $classInfo->class_name,
                'grade_year' => $gradeInfo['year'],
                'init_grade_id' => $init_grade_id
            ];

        } catch (\Exception $e) {
            Log::error('获取班级年级信息失败', [
                'class_id' => $classId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}
