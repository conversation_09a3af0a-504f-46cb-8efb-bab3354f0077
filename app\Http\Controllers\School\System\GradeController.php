<?php

namespace App\Http\Controllers\School\System;

use App\Constants\SchoolConstants;
use App\Http\Controllers\Controller;
use App\Models\School\System\Grade;
use App\Models\School\System\School;
use App\Models\School\System\SchoolCampus;
use Illuminate\Http\Request;

class GradeController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $schoolCampusType = $this->getSchoolCampusType($request);
        // $schoolProvince = $this->getSchoolProvince($request);
        $schoolProvince = $this->getSchoolProvinceBySchoolCampus($request->school_campus_id);

        $isShanghai = SchoolConstants::isShanghai($schoolProvince);

        $query = Grade::select('id', $isShanghai ? 'alisa_name as grade_name' : 'grade_name');

        if ($schoolCampusType) {
            $gradeIds = SchoolConstants::getGradeIdsByType($schoolCampusType, $isShanghai);
            $query->whereIn('id', $gradeIds);
        }

        return $this->success($query->orderBy('id', 'desc')->get());
    }

    /**
     * 获取校区类型
     */
    private function getSchoolCampusType(Request $request): ?int
    {
        if (!$request->school_campus_id) {
            return null;
        }

        return SchoolCampus::find($request->school_campus_id)->type;
    }

    /**
     * 获取学校省份
     */
    private function getSchoolProvince(Request $request): ?string
    {
        return School::find($request->user()->organization->model_id)->province;
    }

    private function getSchoolProvinceBySchoolCampus(int $school_campus_id)
    {
        return School::find( SchoolCampus::find($school_campus_id)->school_id)->province;
    }
    // Methods moved to SchoolConstants class
}
