<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Assessment\PdfZipRequest;
use App\Services\School\Assessment\BatchDownloadPdfService;
use App\Services\School\Assessment\PdfGeneratorService;
use App\Exceptions\BusinessException;
use App\Services\Tool\MessageService;

class PdfZipController extends Controller
{
    protected BatchDownloadPdfService $batchDownloadPdfService;
    protected PdfGeneratorService $pdfGeneratorService;

    public function __construct(
        BatchDownloadPdfService $batchDownloadPdfService,
        PdfGeneratorService $pdfGeneratorService
    ) {
        $this->batchDownloadPdfService = $batchDownloadPdfService;
        $this->pdfGeneratorService = $pdfGeneratorService;
    }

    /**
     * 批量下载测评报告PDF
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDownloadReportPdf(PdfZipRequest $request)
    {
        try {
            $params = $request->validated();
            $params['creator'] = $request->user()->real_name;
            $params['school_id'] = $request->user()->organization->model_id;
            
            list($recordName, $zip_url) = $this->batchDownloadPdfService->handle($params);
            // 发送消息通知
            $message = [
                'title' => 'pdf批量下载成功',
                'content' => '测评任务：'.$recordName.'pdf批量下载成功',
                'type' => 'success',
                'url' => $zip_url,
                'url_type' => 1
            ];
            $messageService = new MessageService();
            $messageService->sendMessage($request->user()->id, $message);

            return $this->success($zip_url);
        } catch (\Exception $e) {
            throw new BusinessException($e->getMessage(), 500);
        }
    }

    /**
     * 生成测评报告PDF
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generatePdf(PdfZipRequest $request)
    {
        $params = $request->validated();
        $assessment_id = $params['assessment_id'];
        $assessment_task_assignment_id = $params['assessment_task_assignment_id'];

        $pdf_url = $this->pdfGeneratorService->generatePdf(
            $assessment_id,
            $assessment_task_assignment_id
        );

        return $this->success($pdf_url);
    }
    
    /**
     * 生成测评报告PDF
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateCompositePdf(PdfZipRequest $request)
    {
        $params = $request->validated();
        $user_id = $params['user_id'] ?? $request->user()->id;
        $module = $params['module'];

        $pdf_url = $this->pdfGeneratorService->generateCompositePdf(
            $user_id,
            $module
        );

        return $this->success($pdf_url);
    }
}
