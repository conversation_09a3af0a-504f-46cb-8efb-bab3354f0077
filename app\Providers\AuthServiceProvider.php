<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // // 自定义 payload
        // \Tymon\JWTAuth\Facades\JWTAuth::customClaims(function ($user) {
        //     return [
        //         'sub' => $user->id,
        //         'name' => $user->name,
        //         'role' => $user->role,  // 自定义字段
        //         'exp' => now()->addDays(30)->timestamp,  // 自定义过期时间
        //     ];
        // });
    }
}
