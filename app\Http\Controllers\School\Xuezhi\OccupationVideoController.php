<?php

namespace App\Http\Controllers\School\Xuezhi;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Xuezhi\OccupationVideoRequest;
use App\Http\Resources\School\Xuezhi\OccupationVideoCollection;
use App\Http\Resources\School\Xuezhi\OccupationVideoResource;
use App\Http\Resources\School\Xuezhi\OccupationTypeResource;
use App\Services\School\Xuezhi\OccupationVideoService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OccupationVideoController extends Controller
{
    protected OccupationVideoService $service;

    public function __construct(OccupationVideoService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取职业视频列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $videos = $this->service->getOccupationVideos($request);
            return $this->success(new OccupationVideoCollection($videos));
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => 'Error: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取职业视频详情
     *
     * @param OccupationVideoRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(OccupationVideoRequest $request, int $id): JsonResponse
    {
        $video = $this->service->getOccupationVideoDetail($id);
        
        if (!$video) {
            return $this->notFound('职业视频不存在');
        }
        
        return $this->success(new OccupationVideoResource($video));
    }

    /**
     * 获取职业类型列表
     *
     * @param OccupationVideoRequest $request
     * @return JsonResponse
     */
    public function occupationTypes(OccupationVideoRequest $request): JsonResponse
    {
        $occupationTypes = $this->service->getOccupationTypes($request);
        
        return $this->success(OccupationTypeResource::collection($occupationTypes));
    }

    /**
     * 获取职业类型详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function occupationTypeDetail(int $id): JsonResponse
    {
        $occupationType = $this->service->getOccupationTypeById($id);
        
        if (!$occupationType) {
            return $this->notFound('职业类型不存在');
        }
        
        return $this->success(new OccupationTypeResource($occupationType));
    }

    /**
     * 获取职业类型及其视频数量统计
     *
     * @return JsonResponse
     */
    public function occupationTypesWithCount(): JsonResponse
    {
        $occupationTypes = $this->service->getOccupationTypeWithVideoCount();
        
        return $this->success(OccupationTypeResource::collection($occupationTypes));
    }
}
