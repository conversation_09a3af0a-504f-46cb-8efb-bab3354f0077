<?php

namespace App\Services\School\Assessment\GroupReport\Career;

use App\Services\School\Assessment\GroupReport\AbstractGroupReportService;

/**
 * 职业兴趣评估团体报告服务类
 * 
 * 该类用于生成霍兰德职业兴趣评估的团体报告，包括兴趣类型分布、兴趣组合分布和得分统计分析
 */
class InterestService extends AbstractGroupReportService
{
    /**
     * 霍兰德类型常量
     * 
     * @var array
     */
    private const HOLLAND_TYPES = [
        'S' => '社会型（S）',
        'R' => '实用型（R）',
        'I' => '研究型（I）',
        'E' => '企业型（E）',
        'C' => '事务型（C）',
        'A' => '艺术型（A）'
    ];

    /**
     * 霍兰德类型常模
     * 
     * @var array
     */
    private const HOLLAND_NORMS = [
        'S' => 49.58,
        'R' => 47.37,
        'I' => 48.93,
        'E' => 47.15,
        'C' => 46.57,
        'A' => 50.40
    ];
    
    /**
     * 霍兰德类型常量
     * 
     * @var array
     */
    private const HOLLAND_CHARACTERISTICS = [
        '社会型（S）' => '人际沟通与协作',
        '实用型（R）' => '具体、实际任务',
        '研究型（I）' => '科学分析类活动',
        '企业型（E）' => '领导、影响他人',
        '事务型（C）' => '常规性工作',
        '艺术型（A）' => '艺术创作'
    ];
    
    /**
     * 霍兰德类型键名常量
     * 
     * @var array
     */
    private const HOLLAND_TYPE_KEYS = ['R', 'I', 'A', 'S', 'E', 'C'];

    /**
     * 生成报告数据
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    protected function generateReportData(
        array $filtered_assessment_with_results, 
        array $class_statistics,
        array $params,
    ): array {
        $processed_data = $this->processAssessmentData($filtered_assessment_with_results, $params['assessment_id'], $class_statistics);

        return [
            'participation_count' => $this->getMemberCount($class_statistics),
            'primary_code_distribution' => $this->calculateInterestDistribution($processed_data),
            'holland_code_distribution' => $this->calculateHollandCodeDistribution($processed_data),
            'interest_averages' => $this->calculateInterestAverages($processed_data)
        ];
    }

    /**
     * 处理评估数据
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param int $assessment_id 评估ID
     * @param array $class_statistics 班级统计数据
     * @return array 处理后的评估数据
     */
    private function processAssessmentData(array $all_assessment_with_results, int $assessment_id, array $class_statistics): array
    {
        return [
            'all_data' => $this->processAllAssessments($all_assessment_with_results, $assessment_id),
            'class_data' => $this->processClassAssessments($class_statistics, $assessment_id),
            'initial_types' => array_fill_keys(self::HOLLAND_TYPE_KEYS, 0)
        ];
    }

    /**
     * 处理班级评估数据
     * 
     * @param array $class_statistics 班级统计数据
     * @param int $assessment_id 评估ID
     * @return array 处理后的班级评估数据
     */
    private function processClassAssessments(array $class_statistics, int $assessment_id): array
    {
        $class_data = [];
        foreach ($class_statistics['stats'] as $class_name => $stats) {
            foreach ($class_statistics['category_data'][$class_name] ?? [] as $student) {
                $result = is_array($student['standard_results']) 
                    ? $student['standard_results'] 
                    : json_decode($student['standard_results'], true);
                
                if (empty($result)) continue;

                $dimensions = $result['dimensions'] ?? [];
                if (empty($dimensions)) continue;

                // 按分数排序维度
                usort($dimensions, function($a, $b) {
                    return $b['score'] - $a['score'];
                });

                $primary_type = $dimensions[0]['code'] ?? '';
                $holland_code = $result['code'] ?? $this->getHollandCodeFromDimensions($dimensions);

                $class_data[$class_name][] = [
                    'class_name' => $class_name,
                    'class_id' => $stats['class_id'],
                    'gender' => $student['gender'],
                    'total_students' => $stats['total'],
                    'interest_scores' => $this->formatInterestScores($dimensions),
                    'holland_code' => $holland_code,
                    'primary_type' => $primary_type,
                    'student_id' => $student['student_id'],
                    'boy_primary_type' => $student['gender'] == 1 ? $primary_type : null,
                    'girl_primary_type' => $student['gender'] != 1 ? $primary_type : null
                ];
            }
        }
        return $class_data;
    }

    /**
     * 格式化兴趣分数
     * 
     * @param array $dimensions 维度数据
     * @return array 格式化后的兴趣分数
     */
    private function formatInterestScores(array $dimensions): array
    {
        $scores = [];
        foreach ($dimensions as $dimension) {
            $scores[] = [$dimension['score'], $dimension['code'], $dimension['name']];
        }
        return $scores;
    }

    /**
     * 从维度数据中获取霍兰德代码
     * 
     * @param array $dimensions 维度数据
     * @return string 霍兰德代码
     */
    private function getHollandCodeFromDimensions(array $dimensions): string
    {
        $codes = array_column($dimensions, 'code');
        return implode('', array_slice($codes, 0, 3));
    }

    /**
     * 计算霍兰德代码分布
     * 
     * @param array $processed_data 处理后的数据
     * @return array 霍兰德代码分布数据
     */
    private function calculateHollandCodeDistribution(array $processed_data): array
    {
        $class_data = $this->flattenClassData($processed_data['class_data']);
        $holland_code_counts = array_count_values(array_column($class_data, 'holland_code'));
        arsort($holland_code_counts);

        return [
            'header'=>[
                'number' => 3,
                'name' => '兴趣组合分析',
            ],
            'default' => [
                'axis' => '',
                'legend' => array_keys($holland_code_counts),
                'series' => array_values($holland_code_counts)
            ],
            'class_distribution' => $this->calculateClassHollandCodeDistribution($class_data),
            'combination_distribution' => $this->combinationDistribution($processed_data['all_data']),
            'combination_summary' => $this->combinationSummary(),
            'consistency_analysis' => $this->consistencyAnalysis($processed_data['all_data']),
        ];
    }

    /**
     * 兴趣组合分析文案
     * 
     * @return string 兴趣一致度数据
     */
    public function combinationSummary(): string
    {
        return '霍兰德兴趣理论通常用个体在六大兴趣类型的得分排名前三的三个类型组合成兴趣的三字码，来描述个体的兴趣特点。例如一个学生在六个兴趣类型中，得分排名前三的依次是企业型（E），研究型（I），社会型（S），那么这个学生的兴趣三字码就是EIS。三字码的兴趣类型组合能够更加充分地体现学生的兴趣特征。
        针对本次测评，图中几类组合兴趣一致度较高，文理倾向较为明显，总占比约为51.13%，其他学生目前的兴趣偏好文理较均衡，倾向暂不明显。';
    }

    /**
     * 兴趣一致度分析
     * 
     * @param array $all_data 所有的兴趣类型数据
     * @return array 兴趣一致度数据
     */
    public function consistencyAnalysis($all_data)
    {
        $high_consistency = ['AES', 'CES', 'CER', 'CIR', 'AIR', 'AIS'];
        $low_consistency = ['AER', 'CIS'];
        $num = count($all_data);

        $high_consistency_count = 0; // 高一致性
        $middle_consistency_count = 0; //中一致性,非高也非低就是中
        $low_consistency_count = 0; //低一致性

        foreach ($all_data as $item) {
            $code_arr = str_split($item['holland_code']);
            sort($code_arr);
            if (in_array(join('', $code_arr), $high_consistency)) {
                $high_consistency_count++;
            } elseif (in_array(join('', $code_arr), $low_consistency)) {
                $low_consistency_count++;
            } else {
                $middle_consistency_count++;
            }
        }

        return [
            [
                'label' => '高一致性',
                'count' => $high_consistency_count,
                'value' => round($high_consistency_count / $num * 100, 2) . '%',
            ],
            [
                'label' => '中一致性',
                'count' => $middle_consistency_count,
                'value' => round($middle_consistency_count / $num * 100, 2). '%',
            ],
            [
                'label' => '低一致性',
                'count' => $low_consistency_count,
                'value' => round($low_consistency_count / $num * 100, 2). '%',
            ],
        ];
    }
    
    /**
     * 计算文理科倾向兴趣类型占比
     * 
     * @param array $all_data 所有的兴趣类型数据
     * @return array 兴趣类型分布数据
     */
    public function combinationDistribution($all_data)
    {
        $arts = ['AES','ACS','AIS'];//文科
        $sciences = ['IRS','AIR','CIR'];//理科

        $num = count($all_data);

        foreach ($all_data as $item) {
            $code_arr = str_split($item['holland_code']);
            sort($code_arr);
            $code_str = join('', $code_arr);
            foreach ($arts as $art) {
                if(!isset($arts_data[$art])){
                    $arts_data[$art] = 0;
                }
                if ($code_str === $art) {
                    $arts_data[$art]++;
                }
            }
            foreach ($sciences as $science) {
                if(!isset($sciences_data[$science])){
                    $sciences_data[$science] = 0;
                }
                if ($code_str === $science) {
                    $sciences_data[$science]++;
                }
            }
        }

        foreach ($arts_data as $key => $value) {
            $rebuild_arts[] = [
                'label' => $key,
                'value' => round($value / $num * 100, 2). '%',
            ];
        }

        foreach ($sciences_data as $k => $v) {
            $rebuild_sciences[] = [
                'label' => $k,
                'value' => round($v / $num * 100, 2). '%',
            ];
        }

        return [
            ['name' => '文科倾向', 'options' => $rebuild_arts],
            ['name' => '理科倾向', 'options' => $rebuild_sciences],
        ];
    }

    /**
     * 计算兴趣类型分布
     * 
     * @param array $processed_data 处理后的数据
     * @return array 兴趣类型分布数据
     */
    private function calculateInterestDistribution(array $processed_data): array
    {
        $all_distribution = $this->calculateAllDistribution($processed_data['all_data']);
        list($primary_percentage,$num_data,$boy_count,$girl_count) = $this->getAllPrimaryCodePercentage($processed_data['all_data']);
        list($gender_data,$boy,$girl) = $this->getNumData($num_data,$boy_count,$girl_count);
        if($boy_count == 0 || $girl_count == 0){
            $distribution_summary = '暂无充足数据用以分析。';
            $gender_summary = '暂无充足数据用以分析。';
        }else{
            $distribution_summary = $this->generateDistributionDescription($all_distribution['sorts']);
            $gender_summary = $this->getGenderSummary($boy,$girl);
        }

        return [
            'header'=>[
                'number' => 2,
                'name' => '兴趣类型分析',
            ],
            'class_distribution' => $this->formatClassTypeData($processed_data['class_data']),
            'distribution_summary' => $distribution_summary,
            'primary_code_percentage' => $primary_percentage,
            'primary_code_summary' => $this->getSummary($primary_percentage),
            'gender_data' => $gender_data,
            'gender_summary' => $gender_summary,
        ];
    }

    /**
     * 六个维度男女生占比
     *
     * @param array $num_data 男女生数据
     * @param int $boy_count 六个维度男生个数
     * @param int $girl_count 六个维度女生个数
     * @return array 六个维度男女生占比,各维度男生总人数-女生总人数，各维度女生总人数
     */
    private function getNumData(array $num_data, int $boy_count, int $girl_count): array
    {
        $data = [];
        foreach ($num_data as $k => $v) {
            $data[] = [
                'label' => self::HOLLAND_TYPES[$k],
                'value' => [
                    'boy' => $boy_count ? round($v['boy'] / $boy_count * 100, 2).'%' : '0%',
                    'girl' =>$girl_count ? round($v['girl'] / $girl_count * 100, 2).'%' : '0%',
                ],
            ];
            $boy[] = [
                'label' => $k,
                'value' => $v['boy'] - $v['girl'],
            ];
            $girl[] = [
                'label' => $k,
                'value' => $v['girl'],
            ];
        }
        
        return [$data,$boy,$girl];
    }

    /**
     * 六个维度男女生占比
     *
     * @param array $boy 男生减去女生的数据
     * @param array $girl 女生数据
     * @return array 六个维度男女生占比文案
     */
    private function getGenderSummary(array $boy, array $girl): string
    {
        array_multisort(array_column($boy, 'value'), SORT_DESC, $boy);
        array_multisort(array_column($girl, 'value'), SORT_DESC, $girl);

        return sprintf('从性别差异分析来看，男生在%s和%s上的人数明显高干女生，而女生在%s和%s上的人数相对较多。这一差异符合一般性别兴趣差异的规律，但也提示教师在教学中应注意打破性别刻板印象，鼓励学生跨越传统性别界限探索多元兴趣。',
        self::HOLLAND_TYPES[$boy[0]['label']],
        self::HOLLAND_TYPES[$boy[1]['label']],
        self::HOLLAND_TYPES[$girl[0]['label']],
        self::HOLLAND_TYPES[$girl[1]['label']]);
    }

    /**
     * 六个维度占比描述
     *
     * @param array $primary_percentage 排序数据
     * @return array 生成的维度占比描述
     */
    private function getSummary(array $primary_percentage): string
    {
        return sprintf('从整体兴趣类型分布来看，%s占比最高，达到%s，其次是%s占%s。%s和%s占比相对较低，分别为%s和%s。这一分布特点表明，本次测评的学生整体上更倾向于%s和%s，对%s和%s的兴趣相对较低。',
        $primary_percentage[0]['label'],
        $primary_percentage[0]['value'],
        $primary_percentage[1]['label'],
        $primary_percentage[1]['value'],
        $primary_percentage[4]['label'],
        $primary_percentage[5]['label'],
        $primary_percentage[4]['value'],
        $primary_percentage[5]['value'],
        self::HOLLAND_CHARACTERISTICS[$primary_percentage[0]['label']],
        self::HOLLAND_CHARACTERISTICS[$primary_percentage[1]['label']],
        self::HOLLAND_CHARACTERISTICS[$primary_percentage[4]['label']],
        self::HOLLAND_CHARACTERISTICS[$primary_percentage[5]['label']]);
    }

    /**
     * 统计兴趣类型首字码比例
     * 
     * @param array $all_data 所有的数据
     * @return array 兴趣类型分布数据百分比，各维度男生女生数据，男生总人数，女生总人数
     */
    private function getAllPrimaryCodePercentage(array $all_data): array
    {
        $num_data = [];
        $boy_count = 0;
        $girl_count = 0;
        $new_data = [];
        foreach(self::HOLLAND_TYPE_KEYS as $v){
            $data[$v] = 0;
        }

        foreach ($all_data as $item) {
            if(!isset($num_data[$item['primary_type']]['boy'])){
                $num_data[$item['primary_type']]['boy'] = 0;
            }
            if(!isset($num_data[$item['primary_type']]['girl'])){
                $num_data[$item['primary_type']]['girl'] = 0;
            }
            $data[$item['primary_type']]++;
            if($item['gender'] == 1){
                $num_data[$item['primary_type']]['boy']++;
                $boy_count++;
            }elseif($item['gender'] == 2){
                $num_data[$item['primary_type']]['girl']++;
                $girl_count++;
            }
        }

        // 按值排序
        arsort($data);

        foreach ($data as $key => $value) {
            $new_data[] = [
                'label' => self::HOLLAND_TYPES[$key],
                'value' => round($value / count($all_data) * 100, 2).'%',
            ];
        }

        return [$new_data,$num_data,$boy_count,$girl_count];
    }


    /**
     * 计算兴趣得分统计
     * 
     * @param array $processed_data 处理后的数据
     * @return array 兴趣得分统计数据
     */
    private function calculateInterestAverages(array $processed_data): array
    {
        $class_data = $this->flattenClassData($processed_data['class_data']);
        $averages = $this->calculateDimensionAverages($class_data);

        return [
            'header'=>[
                'number' => 4,
                'name' => '兴趣类型得分统计',
            ],
            'default' => [
                'axis' => array_values(self::HOLLAND_TYPES),
                'legend' => ['本次','常模'],
                'series' => ['本次'=>array_values($averages['total']),'常模'=>array_values(self::HOLLAND_NORMS)],
                'summary' => $this->getSummaryAverages($averages['total']),
            ],
            'class_averages' => $averages['class'],
            'concern_students' => $this->concernStudents($processed_data['all_data']),
        ];
    }

    /* 
     * 兴趣类型平均分文案
     * @param array $total 本次平均分
     * @return string 兴趣类型平均分文案
     */
    private function getSummaryAverages(array $total): string
    {
        $high = [];
        $middle = [];
        $low = [];
        $positive_diff = [];
        $negative_diff = [];
        foreach ($total as $key => $value) {
            if($value > self::HOLLAND_NORMS[$key]){
                $high[] = self::HOLLAND_TYPES[$key];
            }elseif($value == self::HOLLAND_NORMS[$key]){
                $middle[] = self::HOLLAND_TYPES[$key];
            }else{
                $low[] = self::HOLLAND_TYPES[$key];
            }
            $score = round($value - self::HOLLAND_NORMS[$key], 2);
            if($score >= 0){
                $positive_diff[] = [
                    'label' => self::HOLLAND_TYPES[$key],
                    'value' => round($value - self::HOLLAND_NORMS[$key], 2),
                ];
            }else{
                $negative_diff[] = [
                    'label' => self::HOLLAND_TYPES[$key],
                    'value' => round($value - self::HOLLAND_NORMS[$key], 2),
                ];
            }
        }

        $str = '从整体兴趣类型得分来看，在本次测评中，学生的';
        if ($high) $str .= implode('、', $high) . '的得分高于常模水平，';
        if ($low) $str .= implode('、', $low) . '的得分较常模水平偏低，';
        if ($middle) $str .= implode('、', $middle) . '的得分与常模水平接近。';
        $str .= '其中';
        if ($positive_diff) {
            array_multisort(array_column($positive_diff, 'value'), SORT_DESC, $positive_diff);
            $str .= $positive_diff[0]['label'] . '得分最为突出（超常模' . $positive_diff[0]['value'] . '分），';
        }
        if ($negative_diff) {
            array_multisort(array_column($negative_diff, 'value'), SORT_ASC, $negative_diff);
            $str .= $negative_diff[0]['label'] . '得分差距最大（距离常模' . abs($negative_diff[0]['value']) . '分）。';
        }
        
        return $str;
    }


    /**
     * 计算兴趣模糊型和兴趣缺失型学生
     * 
     * @param array $all_data 所有数据
     * @return array 兴趣模糊型和兴趣缺失型学生列表
     */
    private function concernStudents(array $all_data): array
    {
        // 兴趣模糊型：得分差异小：MAX-MIN＜5
        // 兴趣缺失型：整体得分低：MAX＜43.2
        $ambiguous_interest = [];
        $lack_interest = [];
        foreach($all_data as $value){
            $scores = array_column($value['dimensions'], 'score');
            $max = max($scores);
            $min = min($scores);
            $name = $value['student_name'].'('.$value['grade_year'].'届'.$value['class_name'].')';
            if($max - $min < 5){
                $ambiguous_interest[] = $name;
            }
            if($max < 43.2){
                $lack_interest[] = $name;
            }
        }
        return ['ambiguous'=>$ambiguous_interest,'lack'=>$lack_interest];
    }


    /**
     * 计算所有数据的分布
     * 
     * @param array $all_data 所有数据
     * @return array 所有数据的分布
     */
    private function calculateAllDistribution(array $all_data): array
    {
        $gender_counts = $this->calculateGenderTypeCounts($all_data);
        $total_count = count($all_data);

        return [
            'distributions' => [
                $this->calculatePercentages($gender_counts['boy'], $total_count),
                $this->calculatePercentages($gender_counts['girl'], $total_count)
            ],
            'sorts' => $this->calculateTypeSorts($all_data)
        ];
    }

    /**
     * 计算班级数据的分布
     * 
     * @param array $class_data 班级数据
     * @return array 班级数据的分布
     */
    private function calculateClassDistribution(array $class_data): array
    {
        $flattened_data = $this->flattenClassData($class_data);
        $gender_counts = $this->calculateGenderTypeCounts($flattened_data);
        $total_count = count($flattened_data);

        return [
            'distributions' => [
                $this->calculatePercentages($gender_counts['boy'], $total_count),
                $this->calculatePercentages($gender_counts['girl'], $total_count)
            ]
        ];
    }

    /**
     * 计算性别类型计数
     * 
     * @param array $data 评估数据
     * @return array 性别类型计数数据
     */
    private function calculateGenderTypeCounts(array $data): array
    {
        $init_types = array_fill_keys(self::HOLLAND_TYPE_KEYS, 0);
        $boy_types = array_filter(array_column($data, 'boy_primary_type')); // Remove null values
        $girl_types = array_filter(array_column($data, 'girl_primary_type')); // Remove null values
        
        return [
            'boy' => array_merge($init_types, array_count_values($boy_types)),
            'girl' => array_merge($init_types, array_count_values($girl_types))
        ];
    }

    /**
     * 计算百分比
     * 
     * @param array $counts 计数数据
     * @param int $total 总数
     * @return array 百分比数据
     */
    private function calculatePercentages(array $counts, int $total): array
    {
        krsort($counts);
        return array_map(
            fn($value) => round($value / $total * 100, 2),
            $counts
        );
    }

    /**
     * 格式化班级类型数据
     * 
     * @param array $class_data 班级数据
     * @return array 格式化后的班级类型数据
     */
    private function formatClassTypeData(array $class_data): array
    {
        $class_types = [];

        foreach ($class_data as $class_name => $students) {

            $type_counts = array_count_values(array_column($students, 'primary_type'));

            // 按照 HOLLAND_TYPES 的顺序初始化每个维度的计数

            $ordered_counts = [];

            foreach (array_keys(self::HOLLAND_TYPES) as $type) {

                $ordered_counts[] = $type_counts[$type] ?? 0;

            }

            $class_types[$class_name] = $ordered_counts;

        }

        return [

            'axis' => array_keys($class_data),

            'legend' => array_values(self::HOLLAND_TYPES),

            'series' => $class_types

        ];

    }

    /**
     * 计算维度平均值
     * 
     * @param array $class_data 班级数据
     * @return array 维度平均值数据
     */
    private function calculateDimensionAverages(array $class_data): array
    {
        $class_averages = [];
        $total_scores = array_fill_keys(array_keys(self::HOLLAND_TYPES), 0);
        $total_count = 0;

        foreach ($class_data as $student) {
            $class_name = $student['class_name'];
            if (!isset($class_averages[$class_name])) {
                $class_averages[$class_name] = [
                    'count' => 0,
                    'scores' => array_fill_keys(array_keys(self::HOLLAND_TYPES), 0)
                ];
            }

            foreach ($student['interest_scores'] as $score) {
                $class_averages[$class_name]['scores'][$score[1]] += $score[0];
            }
            $class_averages[$class_name]['count']++;
            $total_count++;
        }

        $formatted_averages = [
            'total' => [],
            'class' => []
        ];

        foreach ($class_averages as $class_name => $data) {
            $formatted_averages['class'][$class_name] = [
                'axis' => array_values(self::HOLLAND_TYPES),
                'legend' => ['本次','常模'],
                'series' => ['本次'=>array_values(array_map(
                    fn($score) => round($score / $data['count'], 2),
                    $data['scores']
                )),'常模'=>array_values(self::HOLLAND_NORMS)],
            ];

            foreach ($data['scores'] as $type => $score) {
                $total_scores[$type] += $score;
            }
        }

        $formatted_averages['total'] = array_map(
            fn($score) => round($score / $total_count, 2),
            $total_scores
        );

        return $formatted_averages;
    }

    /**
     * 扁平化班级数据
     * 
     * @param array $class_data 班级数据
     * @return array 扁平化后的班级数据
     */
    private function flattenClassData(array $class_data): array
    {
        $flattened = [];
        foreach ($class_data as $students) {
            $flattened = array_merge($flattened, $students);
        }
        return $flattened;
    }
    
    /**
     * 计算类型排序
     * 
     * @param array $all_data 所有数据
     * @return array 类型排序数据
     */
    private function calculateTypeSorts(array $all_data): array
    {
        $type_counts = [
            'all' => array_count_values(array_column($all_data, 'primary_type')),
            'boy' => array_count_values(array_filter(array_column($all_data, 'boy_primary_type'))),
            'girl' => array_count_values(array_filter(array_column($all_data, 'girl_primary_type')))
        ];

        arsort($type_counts['all']);
        arsort($type_counts['boy']);
        arsort($type_counts['girl']);

        return [
            'all' => array_keys($type_counts['all']),
            'boy' => array_keys($type_counts['boy']),
            'girl' => array_keys($type_counts['girl'])
        ];
    }

    /**
     * 生成分布描述
     * 
     * @param array $sorts 排序数据
     * @return string 分布描述文本
     */
    private function generateDistributionDescription(array $sorts): string
    {
        $get_type_description = function($type_array) {
            $highest = $type_array[0] ?? array_key_first(self::HOLLAND_TYPES);
            $lowest = end($type_array) ?: array_key_last(self::HOLLAND_TYPES);
            
            return [
                'highest' => self::HOLLAND_TYPES[$highest],
                'lowest' => self::HOLLAND_TYPES[$lowest]
            ];
        };

        $all = $get_type_description($sorts['all']);
        $girl = $get_type_description($sorts['girl']);
        $boy = $get_type_description($sorts['boy']);

        return sprintf(
            '在本次筛选中，%s占比最高，%s占比最低。女生,%s人数比例最高，%s人数比例最低；男生%s人数比例最高，%s人数比例最低。',
            $all['highest'],
            $all['lowest'],
            $girl['highest'],
            $girl['lowest'],
            $boy['highest'],
            $boy['lowest']
        );
    }

    /**
     * 处理所有评估数据
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param int $assessment_id 评估ID
     * @return array 处理后的所有评估数据
     */
    private function processAllAssessments(array $all_assessment_with_results, int $assessment_id): array
    {
        $all_data = [];
        foreach ($all_assessment_with_results as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            if (empty($result)) {
                continue;
            }

            $dimensions = $result['dimensions'] ?? [];
            if (empty($dimensions)) continue;

            // 按分数排序维度
            usort($dimensions, function($a, $b) {
                return $b['score'] - $a['score'];
            });
            
            $primary_type = $dimensions[0]['code'] ?? '';
            $holland_code = $result['code'] ?? $this->getHollandCodeFromDimensions($dimensions);

            $all_data[] = [
                'gender' => $assessment['gender'],
                'holland_code' => $holland_code,
                'dimensions' => $dimensions,
                'student_name' => $assessment['student_name'],
                'class_name' => $assessment['class_name'],
                'grade_year' => $assessment['grade_year'],
                'primary_type' => $primary_type,
                'student_id' => $assessment['student_id'],
                'boy_primary_type' => $assessment['gender'] == 1 ? $primary_type : null,
                'girl_primary_type' => $assessment['gender'] != 1 ? $primary_type : null
            ];
        }
        return $all_data;
    }

    /**
     * 计算班级霍兰德代码分布
     * 
     * @param array $class_data 班级测评数据
     * @return array 班级霍兰德代码分布数据
     */
    private function calculateClassHollandCodeDistribution(array $class_data): array
    {
        $class_distribution = [];
        $class_groups = [];
        
        // 按班级分组数据
        foreach ($class_data as $student) {
            $class_name = $student['class_name'];
            if (!isset($class_groups[$class_name])) {
                $class_groups[$class_name] = [];
            }
            $class_groups[$class_name][] = $student['holland_code'];
        }

        // 计算每个班级的霍兰德代码分布
        foreach ($class_groups as $class_name => $holland_codes) {
            $code_counts = array_count_values($holland_codes);
            arsort($code_counts);
            
            $class_distribution[$class_name] = [
                'axis' => '',
                'legend' => array_keys($code_counts),
                'series' => array_values($code_counts)
            ];
        }

        return $class_distribution;
    }
}