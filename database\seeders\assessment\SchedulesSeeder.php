<?php

namespace Database\Seeders\assessment;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SchedulesSeeder extends BaseIncrementalSeeder
{
    protected string $assessment_type = 'schedules';

    // 测评ID映射
    protected array $survey_ids = [
        1, 18, 21, 28, 45, 32, 53, 72,
        234, 258, 257, 256, 255, 426, 427, 428,
        429, 430, 431, 432, 433, 471, 472, 473
    ];

    // 新老测评ID转换
    protected array $surveytoassessment_id = [
        1  => 1,
        18 => 2,
        21 => 3,
        28 => 4,
        45 => 5,
        32 => 6,
        53 => 7,
        72 => 8,
        234 => 9,
        258 => 10,
        257 => 11,
        256 => 12,
        255 => 13,
        426 => 14,
        427 => 15,
        428 => 16,
        429 => 17,
        430 => 18,
        431 => 19,
        432 => 20,
        471 => 21,
        472 => 22,
        473 => 23,
        433 => 24,
    ];
    /**
     * 获取测评ID列表
     */
    protected function getSurveyIds(): array
    {
        return $this->survey_ids;
    }

    /**
     * 获取评估ID列表
     */
    protected function getAssessmentIds(): array
    {
        return array_values($this->surveytoassessment_id);
    }

    /**
     * 执行具体的seeder逻辑
     */
    protected function executeSeeder(): void
    {
        // 获取最后处理的ID
        $lastProcessedId = $this->getLastProcessedId();

        Log::info("开始执行SchedulesSeeder", [
            'school_id' => $this->school_id,
            'last_processed_id' => $lastProcessedId
        ]);

        // 构建子查询，按 grade_id 和 times survey_id 分组，只查询大于最后处理ID的记录
        $subQuery = DB::connection($this->connect)->table('survey_user_session')
            ->join('survey', 'survey.id', '=', 'survey_user_session.survey_id')
            ->select('grade_id','survey_id','title','times')
            ->where('survey_user_session.school_id', $this->school_id)
            ->whereIn('survey_id', $this->survey_ids)
            ->where('times', '>', 0)
            ->where('survey_user_session.id', '>', $lastProcessedId) // 增量查询条件
            ->groupBy('grade_id','survey_id', 'times');

        $result = DB::connection($this->connect)->table('grade as g')
            ->select(
                'g.id',
                'g.name',
                'g.grade_name',
                'sub.grade_id',
                'sub.survey_id',
                'sub.title',
                'sub.times',
            )
            ->joinSub($subQuery, 'sub', function ($join) {
                $join->on('sub.grade_id', '=', 'g.id');
            })
            ->get()->toArray();

        if (empty($result)) {
            Log::info("没有新的数据需要处理", [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
            return;
        }

        Log::info("找到新数据", [
            'school_id' => $this->school_id,
            'count' => count($result)
        ]);

        $insertedSchedules = [];
        $processedCount = 0;
        $maxSessionId = 0;
        
        // 批量插入准备
        $newSchedulesData = [];
        $oldSchedulesData = [];
        $newTasksData = [];
        $oldTasksData = [];
        $scheduleIdMap = [];
        
        // 用于记录已处理的uniqueKey
        $processedUniqueKeys = [];

        // 第一步：收集所有需要插入的计划数据
        foreach ($result as $item) {
            $scheduleData = [
                'school_id' => $this->school_id,
                'old_times' => $item->times,
                'name' => $item->name . $item->grade_name . $item->title . '第' . $item->times . '次测评计划',
                'open_time' => now()->toDateTimeString(),
                'close_time' => date('Y-m-d H:i:s', strtotime('+10 years')),
                'creator' => '管理员',
            ];

            $uniqueKey = $item->grade_id . '_' . $item->survey_id . '_' . $item->times;

            // 检查uniqueKey是否已经处理过，如果处理过则跳过
            if (!in_array($uniqueKey, $processedUniqueKeys)) {
                // 将当前uniqueKey添加到已处理数组中
                $processedUniqueKeys[] = $uniqueKey;
                
                // 直接收集新计划数据，不再查询数据库
                $newSchedulesData[$uniqueKey] = $scheduleData;
                $scheduleIdMap[$uniqueKey] = ['item' => $item, 'data' => $scheduleData];
            }
            
            $processedCount++;
        }
        
        // 第二步：批量插入新计划
        if (!empty($newSchedulesData)) {
            // 开启事务
            DB::beginTransaction();
            try {
                foreach ($newSchedulesData as $uniqueKey => $scheduleData) {
                    // 插入到 assessment_schedules 表并获取 ID
                    $scheduleId = DB::table('assessment_schedules')->insertGetId($scheduleData);
                    $insertedSchedules[$uniqueKey] = $scheduleId;
                    
                    // 准备老系统数据
                    $oldScheduleData = $scheduleData;
                    $oldScheduleData['id'] = $scheduleId; // 使用相同的ID
                    $oldSchedulesData[] = $oldScheduleData;
                    
                    // 准备任务数据
                    $item = $scheduleIdMap[$uniqueKey]['item'];
                    $tasksData = [
                        'assessment_schedule_id' => $scheduleId,
                        'assessment_id' => $this->surveytoassessment_id[$item->survey_id],
                        'old_survey_id' => $item->survey_id,
                        'old_times' => $item->times,
                        'old_grade_id' => $item->grade_id,
                    ];
                    
                    $newTasksData[] = $tasksData;
                    
                    // 准备老系统任务数据（将在后面批量插入）
                    $oldTaskData = $tasksData;
                    $oldTasksData[] = $oldTaskData;
                }
                
                // 批量插入老系统计划数据
                if (!empty($oldSchedulesData)) {
                    DB::connection($this->connect)->table('assessment_schedules')->insert($oldSchedulesData);
                }
                
                // 批量插入新系统任务数据
                if (!empty($newTasksData)) {
                    $taskIds = [];
                    foreach ($newTasksData as $taskData) {
                        $taskId = DB::table('assessment_tasks')->insertGetId($taskData);
                        $taskIds[] = $taskId;
                    }
                    
                    // 更新老系统任务数据的ID
                    foreach ($oldTasksData as $key => $taskData) {
                        if (isset($taskIds[$key])) {
                            $oldTasksData[$key]['id'] = $taskIds[$key];
                        }
                    }
                    
                    // 批量插入老系统任务数据
                    DB::connection($this->connect)->table('assessment_tasks')->insert($oldTasksData);
                }
                
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error("SchedulesSeeder执行失败", [
                    'school_id' => $this->school_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }
        }

        // 获取当前最大的session ID用于更新执行日志
        $maxSessionId = $this->getMaxSurveySessionId();

        // 更新执行日志
        $this->updateExecutionLog($maxSessionId, $processedCount);

        Log::info("SchedulesSeeder执行完成", [
            'school_id' => $this->school_id,
            'processed_count' => $processedCount,
            'max_session_id' => $maxSessionId
        ]);
    }
}
