<?php

namespace App\Http\Controllers;

use App\Models\ModelChangeLog;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class ModelChangeLogController extends Controller
{
    use CrudOperations;

    protected string $model = ModelChangeLog::class;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $action = $request['action'];
        $model_type = addslashes(string: $request['model_type']);
        $model_id = $request['model_id'];
        $query = ModelChangeLog::query()
            ->when($action, fn($query) => $query->where('action', $action))
            ->when($model_type, fn($query) => $query->where('model_type', 'like', '%' . $model_type))
            ->when($model_id, fn($query) => $query->where('model_id', $model_id));

        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->with(['user:id,real_name'])->get();
        return $this->paginateSuccess($list, $cnt);
    }
    
}
