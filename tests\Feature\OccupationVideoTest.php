<?php

namespace Tests\Feature;

use App\Models\School\Xuezhi\OccupationVideo;
use App\Models\School\Xuezhi\OccupationType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OccupationVideoTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试数据
        $this->createTestData();
    }

    private function createTestData()
    {
        // 创建职业类型
        $occupationType = OccupationType::create([
            'occupation_name' => '计算机类'
        ]);

        // 创建职业视频
        OccupationVideo::create([
            'title' => '软件工程师职业介绍',
            'vid' => 'video123',
            'xzq_id' => 110000,
            'cover' => 'https://example.com/cover.jpg',
            'is_delete' => 0,
            'occupation_type_id' => $occupationType->id,
            'video_id' => 123
        ]);
    }

    public function test_occupation_video_list()
    {
        $response = $this->get('/api/school/xuezhi/occupation-video/list');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'title',
                                'vid',
                                'xzq_id',
                                'cover',
                                'occupation_type_id',
                                'video_id',
                                'occupation_type' => [
                                    'id',
                                    'occupation_name'
                                ]
                            ]
                        ],
                        'pagination'
                    ]
                ]);
    }

    public function test_occupation_video_detail()
    {
        $video = OccupationVideo::first();
        
        $response = $this->get("/api/school/xuezhi/occupation-video/{$video->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'id',
                        'title',
                        'vid',
                        'xzq_id',
                        'cover',
                        'occupation_type_id',
                        'video_id',
                        'occupation_type'
                    ]
                ]);
    }

    public function test_occupation_types_list()
    {
        $response = $this->get('/api/school/xuezhi/occupation-type/list');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'occupation_name'
                        ]
                    ]
                ]);
    }

    public function test_occupation_type_detail()
    {
        $occupationType = OccupationType::first();
        
        $response = $this->get("/api/school/xuezhi/occupation-type/{$occupationType->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'id',
                        'occupation_name'
                    ]
                ]);
    }

    public function test_occupation_types_with_count()
    {
        $response = $this->get('/api/school/xuezhi/occupation-type/with-count');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        '*' => [
                            'id',
                            'occupation_name',
                            'video_count'
                        ]
                    ]
                ]);
    }

    public function test_occupation_video_filter_by_type()
    {
        $occupationType = OccupationType::first();
        
        $response = $this->get("/api/school/xuezhi/occupation-video/list?occupation_type_id={$occupationType->id}");

        $response->assertStatus(200);
    }

    public function test_occupation_video_filter_by_xzq()
    {
        $response = $this->get('/api/school/xuezhi/occupation-video/list?xzq_id=110000');

        $response->assertStatus(200);
    }

    public function test_occupation_video_search_by_title()
    {
        $response = $this->get('/api/school/xuezhi/occupation-video/list?title=软件');

        $response->assertStatus(200);
    }

    public function test_occupation_video_pagination()
    {
        // 测试分页参数
        $response = $this->get('/api/school/xuezhi/occupation-video/list?per_page=10&page=1');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'code',
                    'message',
                    'data' => [
                        'data',
                        'pagination' => [
                            'current_page',
                            'last_page',
                            'per_page',
                            'total',
                            'from',
                            'to',
                            'has_more_pages',
                            'next_page_url',
                            'prev_page_url',
                            'path'
                        ]
                    ]
                ]);
    }

    public function test_occupation_video_pagination_validation()
    {
        // 测试无效的分页参数
        $response = $this->get('/api/school/xuezhi/occupation-video/list?per_page=0');
        $response->assertStatus(422);

        $response = $this->get('/api/school/xuezhi/occupation-video/list?per_page=101');
        $response->assertStatus(422);

        $response = $this->get('/api/school/xuezhi/occupation-video/list?page=0');
        $response->assertStatus(422);

        $response = $this->get('/api/school/xuezhi/occupation-video/list?page=10001');
        $response->assertStatus(422);
    }

    public function test_occupation_video_pagination_edge_cases()
    {
        // 测试边界值
        $response = $this->get('/api/school/xuezhi/occupation-video/list?per_page=1&page=1');
        $response->assertStatus(200);

        $response = $this->get('/api/school/xuezhi/occupation-video/list?per_page=100&page=1');
        $response->assertStatus(200);

        $response = $this->get('/api/school/xuezhi/occupation-video/list?per_page=50&page=10000');
        $response->assertStatus(200);
    }
}
