<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\School\Assessment\PdfGeneratorService;
use Throwable;
use App\Services\School\Assessment\MajorRecommendationService;
use App\Repositories\AssignmentRepository;
use App\Repositories\StudentRepository;
use App\Models\School\Assessment\AssessmentComprehensiveRecommendMajor;

class GenerateCompositePdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    protected $studentRepository;
    protected $assignmentRepository;
    protected $majorRecommendationService;

    /**
     * Create a new job instance.
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
        $this->studentRepository = new StudentRepository();
        $this->assignmentRepository = new AssignmentRepository();
        $this->majorRecommendationService = new MajorRecommendationService();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(PdfGeneratorService $pdfGeneratorService)
    {
        try {
            Log::info('开始生成pdf', ['params' => $this->params]);

            $user_id = $this->params['user_id'];
            $module = $this->params['module'];

            $text = $this->generateMajors($user_id, $module);

            if(empty($text)) return;
            
            # 生成pdf并自动更新状态
            $pdf_url = $pdfGeneratorService->generateCompositePdf(
                $user_id,
                $module
            );

            Log::info('pdf已生成', [
                'pdf_url' => $pdf_url
            ]);
        } catch (\Exception $e) {
            throw new \Exception("pdf生成失败", 500, $e);
        }
    }

    /**
     * 更新推荐专业
     *
     * @return void
     */
    public function generateMajors($user_id, $school_id)
    {
        $assessment_ids = [2,3,4];
        $latestAssignments = $this->assignmentRepository->getLatestAssignments($user_id, $school_id, $assessment_ids);
        if (count($latestAssignments) < 3) {
            return [];
        }
        $latestAssignments = array_column($latestAssignments, null, 'assessment_id');

        $intelligence_result = $latestAssignments[2]['standard_results']['dimensions'];
        array_multisort(array_column($intelligence_result, 'score'), SORT_DESC, $intelligence_result);

        $advantages = array_slice($intelligence_result, 0, $latestAssignments[2]['standard_results']['each_level_count']['advantages']);
        $advantages = implode(',',array_column($advantages,'name'));

        $neutral = array_slice($intelligence_result, 0, $latestAssignments[2]['standard_results']['each_level_count']['neutral']);
        $neutral = implode(',',array_column($neutral,'name'));

        $disadvantages = array_slice($intelligence_result, 0, $latestAssignments[2]['standard_results']['each_level_count']['disadvantages']);
        $disadvantages = implode(',',array_column($disadvantages,'name'));

        $interest = substr($latestAssignments[4]['standard_results']['code'],0,3);
        $personality = $latestAssignments[3]['code'];

        //专业推荐列表，首次调用通义千问服务生成，并入库，后续从库里查询
        $text = $this->majorRecommendationService->generateMajorRecommendations($interest, $personality, $advantages, $neutral, $disadvantages);

        $majors = AssessmentComprehensiveRecommendMajor::where('user_id',$user_id)->get()->toArray();
        if(empty($majors)){
            AssessmentComprehensiveRecommendMajor::create(['user_id'=>$user_id,'majors'=>$text]);
        }else{
            AssessmentComprehensiveRecommendMajor::where('user_id',$user_id)->update(['majors'=>$text]);
        }
        
        return $text;
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        // 向用户发送失败通知等...
    }
}
