<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class Message extends Notification
{
    use Queueable;
    
    /**
     * 通知数据
     * 
     * @var array
     */
    public $notification;
    
    /**
     * 标准通知格式
     * 
     * @var array
     */
    protected $standardFormat = [
        'title' => '',       // 通知标题
        'content' => '',     // 通知内容
        'type' => '',        // 通知类型（如：info, success, error）
        'data' => [],        // 附加数据
        'url' => '',         // 相关链接（可选）
        'url_type' => 1,     // 链接类型：1下载链接 2跳转链接（与url属性匹配使用）（可选）
    ];

    /**
     * Create a new notification instance.
     * 
     * @param array $notification 通知数据
     */
    public function __construct(array $notification)
    {
        // 验证通知数据格式
        $this->validateNotificationFormat($notification);
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = new MailMessage;
        
        // 设置邮件标题
        $mailMessage->subject($this->notification['title']);
        
        // 设置邮件内容
        $mailMessage->line($this->notification['content']);
        
        // 如果有URL，添加操作按钮
        if (!empty($this->notification['url'])) {
            $mailMessage->action('查看详情', $this->notification['url']);
        }
        
        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable)
    {
        return $this->notification;
    }
    
    /**
     * 验证通知格式
     * 
     * @param array $notification
     * @return void
     * @throws \InvalidArgumentException
     */
    protected function validateNotificationFormat(array $notification): void
    {
        // 检查必填字段
        $requiredFields = ['title', 'content', 'type'];
        foreach ($requiredFields as $field) {
            if (!isset($notification[$field]) || empty($notification[$field])) {
                throw new \InvalidArgumentException("通知缺少必填字段: {$field}");
            }
        }
        
        // 验证通知类型
        $validTypes = ['info', 'warning', 'success', 'error'];
        if (!in_array($notification['type'], $validTypes)) {
            throw new \InvalidArgumentException("无效的通知类型: {$notification['type']}");
        }
        
        // 设置默认值
        $notification = array_merge($this->standardFormat, $notification);
        
        $this->notification = $notification;
    }
}
