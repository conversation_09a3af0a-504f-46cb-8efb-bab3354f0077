<?php

namespace Database\Seeders\assessment\pretreatment;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateCompetencyOptionsSeeder extends Seeder
{
    protected string $connect = 'mysql_test';
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 431	创造性思维倾向
        // 432	批判性思维能力
        // 433	沟通与合作能力
        // 434	问题解决能力
        // 435	学习力
        // 436	问题解决能力
        // 437	学习力
        // 使用数据库事务
        DB::transaction(function () {
            // 创造性思维倾向
            $this->processSurveyQuestions('431', 14);
            // 批判性思维能力
            $this->processSurveyQuestions('432', 15);
            // 沟通与合作能力
            $this->processSurveyQuestions('433', 16);
            // 问题解决能力
            $this->processSurveyQuestions('434', 17);
            // 学习力
            $this->processSurveyQuestions('435', 18);
            // 问题解决能力（初中版）
            $this->processSurveyQuestions('436', 19);
            // 学习力（初中版)
            $this->processSurveyQuestions('437', 20);
        });
    }

    public function processSurveyQuestions($surveyId,$assessmentId)
    {
        $surveyQuestionData = DB::connection($this->connect)
        ->table('survey_question')
        ->where('survey_id', $surveyId)
        ->pluck('id')->toArray();
        // dd($surveyQuestionData);
        // 遍历数据，将数据插入到测评表中
        foreach ($surveyQuestionData as $item) {
            // 获取每个问题的选项
            $answersData = DB::connection($this->connect)
                ->table('survey_question_answer')
                ->where('question_id', $item)
                ->orderBy('id') // 使用适当的列来排序选项
                ->get()
                ->toArray();
            
            foreach ($answersData as $k => $v){
                $letter = chr(65 + $k);
                $updates[] = [
                    'id'=>$v->id,
                    'option'=>$letter
                ];
            }
        }
        // dd($updates);
        // 批量更新
        if (!empty($updates)) {
            $connection = DB::connection($this->connect);
            foreach ($updates as $update) {
                $connection->table('survey_question_answer')
                    ->where('id', $update['id'])
                    ->update(['option' => $update['option']]);
            }
        }
        dump('question_answer表中,测评'.$surveyId.'对应option字段更新完毕');
    }

}
